/**
 * Student SWOT Analysis Platform - API Server
 * 
 * This is the main entry point for the backend API server of the Student SWOT Analysis Platform.
 * It sets up an Express server with routes for all entities, connects to MongoDB,
 * and implements authentication, error handling, and other middleware.
 * 
 * The API is designed specifically for Indian schools with support for various boards,
 * term structures, and other India-specific educational requirements.
 */

// Import required packages
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config();

// Import database models
const models = require('../database/schema');

// Create Express app
const app = express();

// Define constants
const PORT = process.env.PORT || 4000;
const API_VERSION = 'v1';
const API_BASE = `/api/${API_VERSION}`;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Set up logging
let accessLogStream;
if (NODE_ENV === 'production') {
  // Create log directory if it doesn't exist
  const logDirectory = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory);
  }
  accessLogStream = fs.createWriteStream(
    path.join(logDirectory, 'access.log'),
    { flags: 'a' }
  );
}

// Configure middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS for all routes
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Parse URL-encoded bodies
app.use(compression()); // Compress responses

// Set up request logging
if (NODE_ENV === 'production') {
  app.use(morgan('combined', { stream: accessLogStream }));
} else {
  app.use(morgan('dev'));
}

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again after 15 minutes'
});

// Apply rate limiting to all API routes
app.use(API_BASE, apiLimiter);

// Database connection
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
})
.catch((err) => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Authentication middleware
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: 'Authorization header missing' });
  }

  const token = authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Token missing' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Role-based access control middleware
const authorize = (roles = []) => {
  if (typeof roles === 'string') {
    roles = [roles];
  }

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Forbidden: Insufficient permissions' });
    }

    next();
  };
};

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', version: API_VERSION });
});

// API documentation route
app.get(`${API_BASE}/docs`, (req, res) => {
  res.status(200).json({
    message: 'Student SWOT Analysis Platform API',
    version: API_VERSION,
    endpoints: [
      { path: '/auth/login', method: 'POST', description: 'Authenticate user' },
      { path: '/auth/register', method: 'POST', description: 'Register new user' },
      { path: '/schools', method: 'GET', description: 'Get all schools' },
      { path: '/students', method: 'GET', description: 'Get all students' },
      // Add other endpoints here
    ]
  });
});

// ===== AUTH ROUTES =====

/**
 * @route   POST /api/v1/auth/login
 * @desc    Authenticate user & get token
 * @access  Public
 */
app.post(`${API_BASE}/auth/login`, async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate request
    if (!username || !password) {
      return res.status(400).json({ error: 'Please provide username and password' });
    }

    // Find user
    const user = await models.User.findOne({ username });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({ error: 'Account is disabled. Please contact administrator' });
    }

    // Validate password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Create payload for JWT
    const payload = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      school_id: user.school_id
    };

    // Update last login
    user.last_login = Date.now();
    await user.save();

    // Sign token
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '1d' },
      (err, token) => {
        if (err) throw err;
        res.json({
          success: true,
          token: token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            role: user.role,
            name: user.profile.name,
            school_id: user.school_id
          }
        });
      }
    );
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error during authentication' });
  }
});

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Private (Admin only)
 */
app.post(`${API_BASE}/auth/register`, authenticateJWT, authorize(['Admin']), async (req, res) => {
  try {
    const { username, email, password, role, school_id, name, phone } = req.body;

    // Validate request
    if (!username || !email || !password || !role || !school_id) {
      return res.status(400).json({ error: 'Please provide all required fields' });
    }

    // Check if user already exists
    const existingUser = await models.User.findOne({ $or: [{ username }, { email }] });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists with this username or email' });
    }

    // Check if school exists
    const school = await models.School.findById(school_id);
    if (!school) {
      return res.status(400).json({ error: 'School not found' });
    }

    // Create new user
    const newUser = new models.User({
      username,
      email,
      password,
      role,
      school_id,
      profile: {
        name,
        phone
      },
      is_active: true
    });

    // Hash password
    const salt = await bcrypt.genSalt(10);
    newUser.password = await bcrypt.hash(password, salt);

    // Save user
    await newUser.save();

    // Create corresponding role-specific record
    if (role === 'Teacher') {
      const teacher = new models.Teacher({
        user_id: newUser._id,
        school_id,
        employee_id: `TCH${Date.now().toString().slice(-6)}`,
        name: {
          first: name.split(' ')[0],
          last: name.split(' ').slice(1).join(' ')
        },
        gender: req.body.gender || 'Other',
        contact: {
          phone,
          email
        },
        is_active: true
      });
      await teacher.save();
    } else if (role === 'Parent') {
      const parent = new models.Parent({
        user_id: newUser._id,
        school_id,
        name: {
          first: name.split(' ')[0],
          last: name.split(' ').slice(1).join(' ')
        },
        relationship: req.body.relationship || 'Guardian',
        contact: {
          phone,
          email
        }
      });
      await parent.save();
    }

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: {
        id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        role: newUser.role
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error during registration' });
  }
});

// ===== SCHOOL ROUTES =====

/**
 * @route   GET /api/v1/schools
 * @desc    Get all schools
 * @access  Private (Admin only)
 */
app.get(`${API_BASE}/schools`, authenticateJWT, authorize(['Admin']), async (req, res) => {
  try {
    const schools = await models.School.find().select('-__v');
    res.json(schools);
  } catch (error) {
    console.error('Error fetching schools:', error);
    res.status(500).json({ error: 'Server error while fetching schools' });
  }
});

/**
 * @route   GET /api/v1/schools/:id
 * @desc    Get school by ID
 * @access  Private
 */
app.get(`${API_BASE}/schools/:id`, authenticateJWT, async (req, res) => {
  try {
    const school = await models.School.findById(req.params.id).select('-__v');
    
    if (!school) {
      return res.status(404).json({ error: 'School not found' });
    }

    // Check if user has access to this school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== school._id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this school' });
    }

    res.json(school);
  } catch (error) {
    console.error('Error fetching school:', error);
    res.status(500).json({ error: 'Server error while fetching school' });
  }
});

/**
 * @route   POST /api/v1/schools
 * @desc    Create a new school
 * @access  Private (Admin only)
 */
app.post(`${API_BASE}/schools`, authenticateJWT, authorize(['Admin']), async (req, res) => {
  try {
    const newSchool = new models.School(req.body);
    const school = await newSchool.save();
    res.status(201).json(school);
  } catch (error) {
    console.error('Error creating school:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating school' });
  }
});

/**
 * @route   PUT /api/v1/schools/:id
 * @desc    Update a school
 * @access  Private (Admin only)
 */
app.put(`${API_BASE}/schools/:id`, authenticateJWT, authorize(['Admin']), async (req, res) => {
  try {
    const school = await models.School.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_at: Date.now() },
      { new: true, runValidators: true }
    );
    
    if (!school) {
      return res.status(404).json({ error: 'School not found' });
    }
    
    res.json(school);
  } catch (error) {
    console.error('Error updating school:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while updating school' });
  }
});

// ===== STUDENT ROUTES =====

/**
 * @route   GET /api/v1/students
 * @desc    Get all students (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/students`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Additional filters
    if (req.query.class_id) {
      query['class.class_id'] = req.query.class_id;
    }
    
    if (req.query.standard) {
      const classIds = await models.Class.find({ 
        standard: req.query.standard,
        school_id: query.school_id
      }).distinct('_id');
      
      query['class.class_id'] = { $in: classIds };
    }
    
    if (req.query.is_active) {
      query.is_active = req.query.is_active === 'true';
    }
    
    // Search by name or admission number
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      query.$or = [
        { 'name.first': searchRegex },
        { 'name.last': searchRegex },
        { admission_number: searchRegex }
      ];
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const students = await models.Student.find(query)
      .populate('class.class_id', 'standard section')
      .select('-__v')
      .sort({ 'name.first': 1, 'name.last': 1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.Student.countDocuments(query);
    
    res.json({
      students,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({ error: 'Server error while fetching students' });
  }
});

/**
 * @route   GET /api/v1/students/:id
 * @desc    Get student by ID
 * @access  Private
 */
app.get(`${API_BASE}/students/:id`, authenticateJWT, async (req, res) => {
  try {
    const student = await models.Student.findById(req.params.id)
      .populate('class.class_id', 'standard section stream')
      .select('-__v');
    
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }
    
    // Check if user has access to this student's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== student.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(student._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
    }
    
    res.json(student);
  } catch (error) {
    console.error('Error fetching student:', error);
    res.status(500).json({ error: 'Server error while fetching student' });
  }
});

/**
 * @route   POST /api/v1/students
 * @desc    Create a new student
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/students`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Validate class exists and belongs to the correct school
    if (req.body.class && req.body.class.class_id) {
      const classExists = await models.Class.findOne({
        _id: req.body.class.class_id,
        school_id: req.body.school_id
      });
      
      if (!classExists) {
        return res.status(400).json({ error: 'Invalid class ID or class does not belong to this school' });
      }
    }
    
    // Check for duplicate admission number
    const existingStudent = await models.Student.findOne({
      admission_number: req.body.admission_number,
      school_id: req.body.school_id
    });
    
    if (existingStudent) {
      return res.status(400).json({ error: 'A student with this admission number already exists in this school' });
    }
    
    // Create new student
    const newStudent = new models.Student(req.body);
    const student = await newStudent.save();
    
    res.status(201).json(student);
  } catch (error) {
    console.error('Error creating student:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating student' });
  }
});

/**
 * @route   PUT /api/v1/students/:id
 * @desc    Update a student
 * @access  Private (Admin, Teacher)
 */
app.put(`${API_BASE}/students/:id`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Find student first to check permissions
    const student = await models.Student.findById(req.params.id);
    
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }
    
    // Check if user has access to this student's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== student.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
    }
    
    // Prevent changing school_id unless admin
    if (req.user.role !== 'Admin' && req.body.school_id && req.body.school_id !== req.user.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: Cannot change student school' });
    }
    
    // Update student
    const updatedStudent = await models.Student.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_at: Date.now() },
      { new: true, runValidators: true }
    ).populate('class.class_id', 'standard section stream');
    
    res.json(updatedStudent);
  } catch (error) {
    console.error('Error updating student:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while updating student' });
  }
});

// ===== ACADEMIC PERFORMANCE ROUTES =====

/**
 * @route   GET /api/v1/academic-performance
 * @desc    Get academic performance records (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/academic-performance`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Student filter
    if (req.query.student_id) {
      query.student_id = req.query.student_id;
    }
    
    // Academic year filter
    if (req.query.academic_year_id) {
      query.academic_year_id = req.query.academic_year_id;
    }
    
    // Term filter
    if (req.query.term_id) {
      query['term.term_id'] = req.query.term_id;
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const academicRecords = await models.AcademicPerformance.find(query)
      .populate('student_id', 'name admission_number class.roll_number')
      .populate('term.term_id', 'name')
      .select('-__v')
      .sort({ 'created_at': -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.AcademicPerformance.countDocuments(query);
    
    res.json({
      academicRecords,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching academic records:', error);
    res.status(500).json({ error: 'Server error while fetching academic records' });
  }
});

/**
 * @route   GET /api/v1/academic-performance/:id
 * @desc    Get academic performance by ID
 * @access  Private
 */
app.get(`${API_BASE}/academic-performance/:id`, authenticateJWT, async (req, res) => {
  try {
    const academicRecord = await models.AcademicPerformance.findById(req.params.id)
      .populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .select('-__v');
    
    if (!academicRecord) {
      return res.status(404).json({ error: 'Academic record not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== academicRecord.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(academicRecord.student_id._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
    }
    
    res.json(academicRecord);
  } catch (error) {
    console.error('Error fetching academic record:', error);
    res.status(500).json({ error: 'Server error while fetching academic record' });
  }
});

/**
 * @route   POST /api/v1/academic-performance
 * @desc    Create a new academic performance record
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/academic-performance`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Validate student exists and belongs to the correct school
    const student = await models.Student.findById(req.body.student_id);
    if (!student) {
      return res.status(400).json({ error: 'Student not found' });
    }
    
    if (student.school_id.toString() !== req.body.school_id.toString()) {
      return res.status(400).json({ error: 'Student does not belong to this school' });
    }
    
    // Validate academic year exists
    const academicYear = await models.AcademicYear.findById(req.body.academic_year_id);
    if (!academicYear) {
      return res.status(400).json({ error: 'Academic year not found' });
    }
    
    // Validate term exists
    const term = await models.AcademicTerm.findById(req.body.term.term_id);
    if (!term) {
      return res.status(400).json({ error: 'Term not found' });
    }
    
    // Check for duplicate record
    const existingRecord = await models.AcademicPerformance.findOne({
      student_id: req.body.student_id,
      academic_year_id: req.body.academic_year_id,
      'term.term_id': req.body.term.term_id,
      'exam_type.category': req.body.exam_type.category
    });
    
    if (existingRecord) {
      return res.status(400).json({ 
        error: 'An academic record already exists for this student, term, and exam type' 
      });
    }
    
    // Calculate percentages and overall results if not provided
    if (req.body.subjects && req.body.subjects.length > 0) {
      let totalMarksObtained = 0;
      let totalPossibleMarks = 0;
      
      req.body.subjects = req.body.subjects.map(subject => {
        // Calculate percentage if not provided
        if (!subject.percentage && subject.marks_obtained !== undefined && subject.total_marks) {
          subject.percentage = (subject.marks_obtained / subject.total_marks) * 100;
        }
        
        // Calculate grade based on percentage if not provided
        if (!subject.grade && subject.percentage !== undefined) {
          subject.grade = calculateGrade(subject.percentage, student.board);
        }
        
        // Calculate grade point based on grade if not provided
        if (!subject.grade_point && subject.grade) {
          subject.grade_point = calculateGradePoint(subject.grade, student.board);
        }
        
        // Add to totals
        totalMarksObtained += subject.marks_obtained || 0;
        totalPossibleMarks += subject.total_marks || 0;
        
        return subject;
      });
      
      // Set overall results if not provided
      if (!req.body.overall_results) {
        req.body.overall_results = {};
      }
      
      if (!req.body.overall_results.total_marks_obtained) {
        req.body.overall_results.total_marks_obtained = totalMarksObtained;
      }
      
      if (!req.body.overall_results.total_possible_marks) {
        req.body.overall_results.total_possible_marks = totalPossibleMarks;
      }
      
      if (!req.body.overall_results.percentage && totalPossibleMarks > 0) {
        req.body.overall_results.percentage = (totalMarksObtained / totalPossibleMarks) * 100;
      }
      
      if (!req.body.overall_results.grade && req.body.overall_results.percentage) {
        req.body.overall_results.grade = calculateGrade(
          req.body.overall_results.percentage, 
          student.board
        );
      }
      
      if (!req.body.overall_results.grade_point && req.body.overall_results.grade) {
        req.body.overall_results.grade_point = calculateGradePoint(
          req.body.overall_results.grade, 
          student.board
        );
      }
      
      if (!req.body.overall_results.division && req.body.overall_results.percentage) {
        req.body.overall_results.division = calculateDivision(
          req.body.overall_results.percentage, 
          student.board
        );
      }
      
      if (!req.body.overall_results.result && req.body.overall_results.percentage) {
        const passingPercentage = getPassingPercentage(student.board);
        req.body.overall_results.result = req.body.overall_results.percentage >= passingPercentage ? 'Pass' : 'Fail';
      }
    }
    
    // Create new academic record
    const newAcademicRecord = new models.AcademicPerformance(req.body);
    const academicRecord = await newAcademicRecord.save();
    
    res.status(201).json(academicRecord);
  } catch (error) {
    console.error('Error creating academic record:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating academic record' });
  }
});

// ===== ATTENDANCE ROUTES =====

/**
 * @route   GET /api/v1/attendance
 * @desc    Get attendance records (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/attendance`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Student filter
    if (req.query.student_id) {
      query.student_id = req.query.student_id;
    }
    
    // Academic year filter
    if (req.query.academic_year_id) {
      query.academic_year_id = req.query.academic_year_id;
    }
    
    // Term filter
    if (req.query.term_id) {
      query['term.term_id'] = req.query.term_id;
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const attendanceRecords = await models.Attendance.find(query)
      .populate('student_id', 'name admission_number class.roll_number')
      .populate('term.term_id', 'name')
      .select('-__v')
      .sort({ 'created_at': -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.Attendance.countDocuments(query);
    
    res.json({
      attendanceRecords,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    res.status(500).json({ error: 'Server error while fetching attendance records' });
  }
});

/**
 * @route   GET /api/v1/attendance/:id
 * @desc    Get attendance by ID
 * @access  Private
 */
app.get(`${API_BASE}/attendance/:id`, authenticateJWT, async (req, res) => {
  try {
    const attendanceRecord = await models.Attendance.findById(req.params.id)
      .populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .select('-__v');
    
    if (!attendanceRecord) {
      return res.status(404).json({ error: 'Attendance record not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== attendanceRecord.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(attendanceRecord.student_id._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
    }
    
    res.json(attendanceRecord);
  } catch (error) {
    console.error('Error fetching attendance record:', error);
    res.status(500).json({ error: 'Server error while fetching attendance record' });
  }
});

/**
 * @route   POST /api/v1/attendance
 * @desc    Create a new attendance record
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/attendance`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Validate student exists and belongs to the correct school
    const student = await models.Student.findById(req.body.student_id);
    if (!student) {
      return res.status(400).json({ error: 'Student not found' });
    }
    
    if (student.school_id.toString() !== req.body.school_id.toString()) {
      return res.status(400).json({ error: 'Student does not belong to this school' });
    }
    
    // Validate academic year exists
    const academicYear = await models.AcademicYear.findById(req.body.academic_year_id);
    if (!academicYear) {
      return res.status(400).json({ error: 'Academic year not found' });
    }
    
    // Validate term exists
    const term = await models.AcademicTerm.findById(req.body.term.term_id);
    if (!term) {
      return res.status(400).json({ error: 'Term not found' });
    }
    
    // Check for duplicate record
    const existingRecord = await models.Attendance.findOne({
      student_id: req.body.student_id,
      academic_year_id: req.body.academic_year_id,
      'term.term_id': req.body.term.term_id
    });
    
    if (existingRecord) {
      return res.status(400).json({ 
        error: 'An attendance record already exists for this student and term' 
      });
    }
    
    // Calculate attendance percentages and term summary if not provided
    if (req.body.monthly_records && req.body.monthly_records.length > 0) {
      req.body.monthly_records = req.body.monthly_records.map(month => {
        // Calculate attendance percentage if not provided
        if (!month.attendance_percentage && month.working_days > 0) {
          month.attendance_percentage = (month.present_days / month.working_days) * 100;
        }
        
        return month;
      });
      
      // Calculate term summary if not provided
      if (!req.body.term_summary) {
        req.body.term_summary = {
          total_working_days: 0,
          total_present: 0,
          total_absent: 0,
          total_leaves: {
            medical: 0,
            pre_approved: 0,
            unauthorized: 0
          },
          total_late_arrivals: 0
        };
      }
      
      // Sum up monthly records for term summary
      req.body.monthly_records.forEach(month => {
        req.body.term_summary.total_working_days += month.working_days || 0;
        req.body.term_summary.total_present += month.present_days || 0;
        req.body.term_summary.total_absent += month.absent_days || 0;
        
        if (month.leave) {
          req.body.term_summary.total_leaves.medical += month.leave.medical || 0;
          req.body.term_summary.total_leaves.pre_approved += month.leave.pre_approved || 0;
          req.body.term_summary.total_leaves.unauthorized += month.leave.unauthorized || 0;
        }
        
        req.body.term_summary.total_late_arrivals += month.late_arrivals || 0;
      });
      
      // Calculate attendance percentage for term
      if (req.body.term_summary.total_working_days > 0) {
        req.body.term_summary.attendance_percentage = 
          (req.body.term_summary.total_present / req.body.term_summary.total_working_days) * 100;
      }
    }
    
    // Create new attendance record
    const newAttendanceRecord = new models.Attendance(req.body);
    const attendanceRecord = await newAttendanceRecord.save();
    
    res.status(201).json(attendanceRecord);
  } catch (error) {
    console.error('Error creating attendance record:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating attendance record' });
  }
});

// ===== BEHAVIOR ROUTES =====

/**
 * @route   GET /api/v1/behavior
 * @desc    Get behavior records (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/behavior`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Student filter
    if (req.query.student_id) {
      query.student_id = req.query.student_id;
    }
    
    // Academic year filter
    if (req.query.academic_year_id) {
      query.academic_year_id = req.query.academic_year_id;
    }
    
    // Term filter
    if (req.query.term_id) {
      query['term.term_id'] = req.query.term_id;
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const behaviorRecords = await models.Behavior.find(query)
      .populate('student_id', 'name admission_number class.roll_number')
      .populate('term.term_id', 'name')
      .select('-__v')
      .sort({ 'created_at': -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.Behavior.countDocuments(query);
    
    res.json({
      behaviorRecords,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching behavior records:', error);
    res.status(500).json({ error: 'Server error while fetching behavior records' });
  }
});

/**
 * @route   GET /api/v1/behavior/:id
 * @desc    Get behavior record by ID
 * @access  Private
 */
app.get(`${API_BASE}/behavior/:id`, authenticateJWT, async (req, res) => {
  try {
    const behaviorRecord = await models.Behavior.findById(req.params.id)
      .populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .select('-__v');
    
    if (!behaviorRecord) {
      return res.status(404).json({ error: 'Behavior record not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== behaviorRecord.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(behaviorRecord.student_id._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
    }
    
    res.json(behaviorRecord);
  } catch (error) {
    console.error('Error fetching behavior record:', error);
    res.status(500).json({ error: 'Server error while fetching behavior record' });
  }
});

/**
 * @route   POST /api/v1/behavior
 * @desc    Create a new behavior record
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/behavior`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Validate student exists and belongs to the correct school
    const student = await models.Student.findById(req.body.student_id);
    if (!student) {
      return res.status(400).json({ error: 'Student not found' });
    }
    
    if (student.school_id.toString() !== req.body.school_id.toString()) {
      return res.status(400).json({ error: 'Student does not belong to this school' });
    }
    
    // Validate academic year exists
    const academicYear = await models.AcademicYear.findById(req.body.academic_year_id);
    if (!academicYear) {
      return res.status(400).json({ error: 'Academic year not found' });
    }
    
    // Validate term exists
    const term = await models.AcademicTerm.findById(req.body.term.term_id);
    if (!term) {
      return res.status(400).json({ error: 'Term not found' });
    }
    
    // Check for duplicate record
    const existingRecord = await models.Behavior.findOne({
      student_id: req.body.student_id,
      academic_year_id: req.body.academic_year_id,
      'term.term_id': req.body.term.term_id
    });
    
    if (existingRecord) {
      return res.status(400).json({ 
        error: 'A behavior record already exists for this student and term' 
      });
    }
    
    // Create new behavior record
    const newBehaviorRecord = new models.Behavior(req.body);
    const behaviorRecord = await newBehaviorRecord.save();
    
    res.status(201).json(behaviorRecord);
  } catch (error) {
    console.error('Error creating behavior record:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating behavior record' });
  }
});

// ===== EXTRACURRICULAR ROUTES =====

/**
 * @route   GET /api/v1/extracurricular
 * @desc    Get extracurricular records (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/extracurricular`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Student filter
    if (req.query.student_id) {
      query.student_id = req.query.student_id;
    }
    
    // Academic year filter
    if (req.query.academic_year_id) {
      query.academic_year_id = req.query.academic_year_id;
    }
    
    // Term filter
    if (req.query.term_id) {
      query['term.term_id'] = req.query.term_id;
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const extracurricularRecords = await models.Extracurricular.find(query)
      .populate('student_id', 'name admission_number class.roll_number')
      .populate('term.term_id', 'name')
      .select('-__v')
      .sort({ 'created_at': -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.Extracurricular.countDocuments(query);
    
    res.json({
      extracurricularRecords,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching extracurricular records:', error);
    res.status(500).json({ error: 'Server error while fetching extracurricular records' });
  }
});

/**
 * @route   GET /api/v1/extracurricular/:id
 * @desc    Get extracurricular record by ID
 * @access  Private
 */
app.get(`${API_BASE}/extracurricular/:id`, authenticateJWT, async (req, res) => {
  try {
    const extracurricularRecord = await models.Extracurricular.findById(req.params.id)
      .populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .select('-__v');
    
    if (!extracurricularRecord) {
      return res.status(404).json({ error: 'Extracurricular record not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== extracurricularRecord.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(extracurricularRecord.student_id._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
    }
    
    res.json(extracurricularRecord);
  } catch (error) {
    console.error('Error fetching extracurricular record:', error);
    res.status(500).json({ error: 'Server error while fetching extracurricular record' });
  }
});

/**
 * @route   POST /api/v1/extracurricular
 * @desc    Create a new extracurricular record
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/extracurricular`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Validate student exists and belongs to the correct school
    const student = await models.Student.findById(req.body.student_id);
    if (!student) {
      return res.status(400).json({ error: 'Student not found' });
    }
    
    if (student.school_id.toString() !== req.body.school_id.toString()) {
      return res.status(400).json({ error: 'Student does not belong to this school' });
    }
    
    // Validate academic year exists
    const academicYear = await models.AcademicYear.findById(req.body.academic_year_id);
    if (!academicYear) {
      return res.status(400).json({ error: 'Academic year not found' });
    }
    
    // Validate term exists
    const term = await models.AcademicTerm.findById(req.body.term.term_id);
    if (!term) {
      return res.status(400).json({ error: 'Term not found' });
    }
    
    // Check for duplicate record
    const existingRecord = await models.Extracurricular.findOne({
      student_id: req.body.student_id,
      academic_year_id: req.body.academic_year_id,
      'term.term_id': req.body.term.term_id
    });
    
    if (existingRecord) {
      return res.status(400).json({ 
        error: 'An extracurricular record already exists for this student and term' 
      });
    }
    
    // Create new extracurricular record
    const newExtracurricularRecord = new models.Extracurricular(req.body);
    const extracurricularRecord = await newExtracurricularRecord.save();
    
    res.status(201).json(extracurricularRecord);
  } catch (error) {
    console.error('Error creating extracurricular record:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating extracurricular record' });
  }
});

// ===== SWOT ANALYSIS ROUTES =====

/**
 * @route   GET /api/v1/swot-analysis
 * @desc    Get SWOT analysis records (with filtering)
 * @access  Private
 */
app.get(`${API_BASE}/swot-analysis`, authenticateJWT, async (req, res) => {
  try {
    // Build query based on user role and filters
    const query = {};
    
    // School-specific filter based on user's school
    if (req.user.role !== 'Admin') {
      query.school_id = req.user.school_id;
    } else if (req.query.school_id) {
      query.school_id = req.query.school_id;
    }
    
    // Student filter
    if (req.query.student_id) {
      query.student_id = req.query.student_id;
    }
    
    // Academic year filter
    if (req.query.academic_year_id) {
      query.academic_year_id = req.query.academic_year_id;
    }
    
    // Term filter
    if (req.query.term_id) {
      query['term.term_id'] = req.query.term_id;
    }
    
    // Generation method filter
    if (req.query.generation_method) {
      query.generation_method = req.query.generation_method;
    }
    
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const swotAnalyses = await models.SWOTAnalysis.find(query)
      .populate('student_id', 'name admission_number class.roll_number')
      .populate('term.term_id', 'name')
      .populate('generated_by', 'username profile.name')
      .select('-__v')
      .sort({ 'generated_at': -1 })
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await models.SWOTAnalysis.countDocuments(query);
    
    res.json({
      swotAnalyses,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching SWOT analyses:', error);
    res.status(500).json({ error: 'Server error while fetching SWOT analyses' });
  }
});

/**
 * @route   GET /api/v1/swot-analysis/:id
 * @desc    Get SWOT analysis by ID
 * @access  Private
 */
app.get(`${API_BASE}/swot-analysis/:id`, authenticateJWT, async (req, res) => {
  try {
    const swotAnalysis = await models.SWOTAnalysis.findById(req.params.id)
      .populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .populate('generated_by', 'username profile.name')
      .select('-__v');
    
    if (!swotAnalysis) {
      return res.status(404).json({ error: 'SWOT analysis not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== swotAnalysis.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // If user is a parent, check if they are related to this student
    if (req.user.role === 'Parent') {
      const parent = await models.Parent.findOne({ user_id: req.user.id });
      if (!parent || !parent.children.includes(swotAnalysis.student_id._id)) {
        return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
      }
      
      // Update parent_viewed_at if not already set
      if (!swotAnalysis.parent_viewed_at) {
        swotAnalysis.parent_viewed_at = Date.now();
        swotAnalysis.is_shared_with_parent = true;
        await swotAnalysis.save();
      }
    }
    
    res.json(swotAnalysis);
  } catch (error) {
    console.error('Error fetching SWOT analysis:', error);
    res.status(500).json({ error: 'Server error while fetching SWOT analysis' });
  }
});

/**
 * @route   POST /api/v1/swot-analysis
 * @desc    Create a new SWOT analysis
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/swot-analysis`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Ensure school_id is set based on user's school (unless admin)
    if (req.user.role !== 'Admin') {
      req.body.school_id = req.user.school_id;
    }
    
    // Set generated_by to current user
    req.body.generated_by = req.user.id;
    
    // Validate student exists and belongs to the correct school
    const student = await models.Student.findById(req.body.student_id);
    if (!student) {
      return res.status(400).json({ error: 'Student not found' });
    }
    
    if (student.school_id.toString() !== req.body.school_id.toString()) {
      return res.status(400).json({ error: 'Student does not belong to this school' });
    }
    
    // Validate academic year exists
    const academicYear = await models.AcademicYear.findById(req.body.academic_year_id);
    if (!academicYear) {
      return res.status(400).json({ error: 'Academic year not found' });
    }
    
    // Validate term exists
    const term = await models.AcademicTerm.findById(req.body.term.term_id);
    if (!term) {
      return res.status(400).json({ error: 'Term not found' });
    }
    
    // Check for duplicate record
    const existingAnalysis = await models.SWOTAnalysis.findOne({
      student_id: req.body.student_id,
      academic_year_id: req.body.academic_year_id,
      'term.term_id': req.body.term.term_id
    });
    
    if (existingAnalysis) {
      return res.status(400).json({ 
        error: 'A SWOT analysis already exists for this student and term' 
      });
    }
    
    // Create new SWOT analysis
    const newSWOTAnalysis = new models.SWOTAnalysis(req.body);
    const swotAnalysis = await newSWOTAnalysis.save();
    
    res.status(201).json(swotAnalysis);
  } catch (error) {
    console.error('Error creating SWOT analysis:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while creating SWOT analysis' });
  }
});

/**
 * @route   PUT /api/v1/swot-analysis/:id
 * @desc    Update a SWOT analysis
 * @access  Private (Admin, Teacher)
 */
app.put(`${API_BASE}/swot-analysis/:id`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    // Find SWOT analysis first to check permissions
    const swotAnalysis = await models.SWOTAnalysis.findById(req.params.id);
    
    if (!swotAnalysis) {
      return res.status(404).json({ error: 'SWOT analysis not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== swotAnalysis.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // Update SWOT analysis
    const updatedSWOTAnalysis = await models.SWOTAnalysis.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_at: Date.now() },
      { new: true, runValidators: true }
    ).populate('student_id', 'name admission_number class')
      .populate('academic_year_id', 'name')
      .populate('term.term_id', 'name')
      .populate('generated_by', 'username profile.name');
    
    res.json(updatedSWOTAnalysis);
  } catch (error) {
    console.error('Error updating SWOT analysis:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Server error while updating SWOT analysis' });
  }
});

/**
 * @route   POST /api/v1/swot-analysis/:id/share
 * @desc    Share SWOT analysis with parent
 * @access  Private (Admin, Teacher)
 */
app.post(`${API_BASE}/swot-analysis/:id/share`, authenticateJWT, authorize(['Admin', 'Teacher']), async (req, res) => {
  try {
    const swotAnalysis = await models.SWOTAnalysis.findById(req.params.id);
    
    if (!swotAnalysis) {
      return res.status(404).json({ error: 'SWOT analysis not found' });
    }
    
    // Check if user has access to this record's school
    if (req.user.role !== 'Admin' && req.user.school_id.toString() !== swotAnalysis.school_id.toString()) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this record' });
    }
    
    // Update sharing status
    swotAnalysis.is_shared_with_parent = true;
    await swotAnalysis.save();
    
    // TODO: Send notification to parent
    
    res.json({ 
      success: true, 
      message: 'SWOT analysis shared with parent successfully' 
    });
  } catch (error) {
    console.error('Error sharing SWOT analysis:', error);
    res.status(500).json({ error: 'Server error while sharing SWOT analysis' });
  }
});

/**
 * @route   POST /api/v1/swot-analysis/:id/feedback
 * @desc    Add parent feedback to SWOT analysis
 * @access  Private (Parent)
 */
app.post(`${API_BASE}/swot-analysis/:id/feedback`, authenticateJWT, authorize(['Parent']), async (req, res) => {
  try {
    const swotAnalysis = await models.SWOTAnalysis.findById(req.params.id);
    
    if (!swotAnalysis) {
      return res.status(404).json({ error: 'SWOT analysis not found' });
    }
    
    // Check if user has access to this student
    const parent = await models.Parent.findOne({ user_id: req.user.id });
    if (!parent || !parent.children.includes(swotAnalysis.student_id)) {
      return res.status(403).json({ error: 'Forbidden: You do not have access to this student' });
    }
    
    // Update parent feedback
    swotAnalysis.parent_feedback = req.body.feedback;
    await swotAnalysis.save();
    
    res.json({ 
      success: true, 
      message: 'Feedback added successfully' 
    });
  } catch (error) {
    console.error('Error adding feedback:', error);
    res.status(500).json({ error: 'Server error while adding feedback' });
  }
});

// ===== HELPER FUNCTIONS =====

/**
 * Calculate grade based on percentage and board
 * @param {number} percentage - Percentage score
 * @param {string} board - Education board
 * @returns {string} - Grade
 */
function calculateGrade(percentage, board) {
  // Default to CBSE grading
  if (!board || board === 'CBSE') {
    if (percentage >= 91) return 'A1';
    if (percentage >= 81) return 'A2';
    if (percentage >= 71) return 'B1';
    if (percentage >= 61) return 'B2';
    if (percentage >= 51) return 'C1';
    if (percentage >= 41) return 'C2';
    if (percentage >= 33) return 'D';
    return 'E';
  }
  
  // ICSE grading
  if (board === 'ICSE') {
    if (percentage >= 91) return 'A1';
    if (percentage >= 81) return 'A2';
    if (percentage >= 71) return 'B1';
    if (percentage >= 61) return 'B2';
    if (percentage >= 51) return 'C1';
    if (percentage >= 41) return 'C2';
    if (percentage >= 33) return 'D';
    if (percentage >= 21) return 'E';
    return 'F';
  }
  
  // State board (generic)
  if (board === 'State') {
    if (percentage >= 75) return 'A';
    if (percentage >= 60) return 'B';
    if (percentage >= 45) return 'C';
    if (percentage >= 35) return 'D';
    return 'F';
  }
  
  // International boards
  if (board === 'IB' || board === 'Cambridge') {
    if (percentage >= 90) return 'A*';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B';
    if (percentage >= 60) return 'C';
    if (percentage >= 50) return 'D';
    if (percentage >= 40) return 'E';
    return 'F';
  }
  
  // Default fallback
  if (percentage >= 90) return 'A';
  if (percentage >= 80) return 'B';
  if (percentage >= 70) return 'C';
  if (percentage >= 60) return 'D';
  if (percentage >= 50) return 'E';
  return 'F';
}

/**
 * Calculate grade point based on grade and board
 * @param {string} grade - Letter grade
 * @param {string} board - Education board
 * @returns {number} - Grade point
 */
function calculateGradePoint(grade, board) {
  // CBSE 10-point system
  if (!board || board === 'CBSE') {
    switch (grade) {
      case 'A1': return 10.0;
      case 'A2': return 9.0;
      case 'B1': return 8.0;
      case 'B2': return 7.0;
      case 'C1': return 6.0;
      case 'C2': return 5.0;
      case 'D': return 4.0;
      default: return 0.0;
    }
  }
  
  // ICSE doesn't traditionally use grade points, but we'll define a mapping
  if (board === 'ICSE') {
    switch (grade) {
      case 'A1': return 10.0;
      case 'A2': return 9.0;
      case 'B1': return 8.0;
      case 'B2': return 7.0;
      case 'C1': return 6.0;
      case 'C2': return 5.0;
      case 'D': return 4.0;
      case 'E': return 2.0;
      default: return 0.0;
    }
  }
  
  // State board (generic)
  if (board === 'State') {
    switch (grade) {
      case 'A': return 10.0;
      case 'B': return 8.0;
      case 'C': return 6.0;
      case 'D': return 4.0;
      default: return 0.0;
    }
  }
  
  // International boards
  if (board === 'IB' || board === 'Cambridge') {
    switch (grade) {
      case 'A*': return 10.0;
      case 'A': return 9.0;
      case 'B': return 8.0;
      case 'C': return 7.0;
      case 'D': return 6.0;
      case 'E': return 5.0;
      default: return 0.0;
    }
  }
  
  // Default fallback
  switch (grade) {
    case 'A': return 10.0;
    case 'B': return 8.0;
    case 'C': return 6.0;
    case 'D': return 4.0;
    case 'E': return 2.0;
    default: return 0.0;
  }
}

/**
 * Calculate division based on percentage and board
 * @param {number} percentage - Percentage score
 * @param {string} board - Education board
 * @returns {string} - Division
 */
function calculateDivision(percentage, board) {
  // Most Indian boards follow a similar division system
  if (percentage >= 60) return 'First';
  if (percentage >= 45) return 'Second';
  if (percentage >= 33) return 'Third';
  return 'Fail';
}

/**
 * Get passing percentage based on board
 * @param {string} board - Education board
 * @returns {number} - Passing percentage
 */
function getPassingPercentage(board) {
  switch (board) {
    case 'CBSE': return 33;
    case 'ICSE': return 33;
    case 'State': return 35;
    case 'IB': return 40;
    case 'Cambridge': return 40;
    default: return 33;
  }
}

// ===== ERROR HANDLING MIDDLEWARE =====

// 404 handler
app.use((req, res, next) => {
  res.status(404).json({ error: 'Route not found' });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  res.status(statusCode).json({
    error: message,
    stack: NODE_ENV === 'development' ? err.stack : undefined
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API base URL: ${API_BASE}`);
  console.log(`Environment: ${NODE_ENV}`);
});

module.exports = app;
