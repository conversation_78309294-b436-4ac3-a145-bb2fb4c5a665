import React, { useState, createContext, useContext, useEffect, Suspense } from 'react';
import { BrowserRouter, Routes, Route, Link, Outlet, Navigate, useNavigate } from 'react-router-dom';
import { ThemeProvider, createTheme, CssBaseline, AppBar, Toolbar, Typography, Drawer, List, ListItem, ListItemIcon, ListItemText, Box, CircularProgress, Button, Select, MenuItem, FormControl, InputLabel, Container } from '@mui/material';
import { Dashboard as DashboardIcon, People as PeopleIcon, Assessment as AssessmentIcon, Settings as SettingsIcon, ExitToApp as ExitToAppIcon, Login as LoginIcon, Language as LanguageIcon } from '@mui/icons-material';
import i18n from 'i18next';
import { initReactI18next, useTranslation } from 'react-i18next';
import HttpApi from 'i18next-http-backend'; // To load translations from files

// --- 1. CONTEXTS (Authentication and Application Settings) ---

// Authentication Context
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(localStorage.getItem('isAuthenticated') === 'true');
  const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));
  // In a real app, token would be stored, and user info fetched or stored upon login

  const login = (userData) => {
    // Simulate API call for login
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser = { username: userData.username, role: 'teacher', school_id: 'school123', name: 'Demo User' };
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('user', JSON.stringify(mockUser));
        setIsAuthenticated(true);
        setUser(mockUser);
        resolve(mockUser);
      }, 500);
    });
  };

  const logout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);

// Application Settings Context (for language, theme, etc.)
const AppSettingsContext = createContext();

export const AppSettingsProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('appLanguage') || 'en');

  useEffect(() => {
    i18n.changeLanguage(language);
    localStorage.setItem('appLanguage', language);
  }, [language]);

  const changeLanguage = (lang) => {
    setLanguage(lang);
  };

  return (
    <AppSettingsContext.Provider value={{ language, changeLanguage }}>
      {children}
    </AppSettingsContext.Provider>
  );
};

export const useAppSettings = () => useContext(AppSettingsContext);


// --- 2. i18n INTERNATIONALIZATION SETUP ---
i18n
  .use(HttpApi) // Use HttpApi backend to load translations
  .use(initReactI18next) // Passes i18n down to react-i18next
  .init({
    supportedLngs: ['en', 'hi'],
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development', // Enable debug in development
    interpolation: {
      escapeValue: false, // React already safes from xss
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json', // Path to translation files in public folder
    },
    ns: ['common', 'dashboard', 'login'], // Namespaces
    defaultNS: 'common',
  });


// --- 3. THEME CONFIGURATION (Material UI) ---
const theme = createTheme({
  palette: {
    primary: {
      main: '#2E5BA8', // Deep Blue (India-Optimized)
    },
    secondary: {
      main: '#FF9933', // Saffron (India-Optimized)
    },
    background: {
      default: '#f4f6f8',
    },
  },
  typography: {
    fontFamily: '"Noto Sans", "Roboto", "Arial", sans-serif', // Include Noto Sans for better Indic script support
    h5: {
      fontWeight: 600,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#2E5BA8',
        }
      }
    }
  }
});

// --- 4. LAYOUT COMPONENTS ---
const drawerWidth = 240;

// Header Component
const Header = () => {
  const { t } = useTranslation('common');
  const { logout, user } = useAuth();
  const { language, changeLanguage } = useAppSettings();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleLanguageChange = (event) => {
    changeLanguage(event.target.value);
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
          {t('platformTitle')}
        </Typography>
        <FormControl size="small" sx={{ m: 1, minWidth: 120 }}>
          <InputLabel id="language-select-label">{t('language')}</InputLabel>
          <Select
            labelId="language-select-label"
            id="language-select"
            value={language}
            label={t('language')}
            onChange={handleLanguageChange}
            startIcon={<LanguageIcon />}
          >
            <MenuItem value="en">English</MenuItem>
            <MenuItem value="hi">हिन्दी (Hindi)</MenuItem>
          </Select>
        </FormControl>
        {user && <Typography sx={{ mr: 2 }}>{t('welcome')}, {user.name || user.username}</Typography>}
        <Button color="inherit" onClick={handleLogout} startIcon={<ExitToAppIcon />}>
          {t('logout')}
        </Button>
      </Toolbar>
    </AppBar>
  );
};

// Sidebar Component
const Sidebar = () => {
  const { t } = useTranslation('common');
  const menuItems = [
    { text: t('dashboard'), icon: <DashboardIcon />, path: '/dashboard' },
    { text: t('students'), icon: <PeopleIcon />, path: '/students' },
    { text: t('reports'), icon: <AssessmentIcon />, path: '/reports' },
    { text: t('settings'), icon: <SettingsIcon />, path: '/settings' },
  ];

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box', backgroundColor: '#2E5BA8', color: 'white' },
      }}
    >
      <Toolbar /> {/* For spacing under the AppBar */}
      <Box sx={{ overflow: 'auto' }}>
        <List>
          {menuItems.map((item) => (
            <ListItem button component={Link} to={item.path} key={item.text} sx={{
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
              '& .MuiListItemIcon-root': {
                color: 'white',
              }
            }}>
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItem>
          ))}
        </List>
      </Box>
    </Drawer>
  );
};

// Main Layout for Authenticated Routes
const MainLayout = () => {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <Header />
      <Sidebar />
      <Box
        component="main"
        sx={{ flexGrow: 1, bgcolor: 'background.default', p: 3, width: `calc(100% - ${drawerWidth}px)` }}
      >
        <Toolbar /> {/* For spacing under the AppBar */}
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet /> {/* Nested routes will render here */}
        </Suspense>
      </Box>
    </Box>
  );
};

// --- 5. PAGE COMPONENTS (Placeholders) ---
// These would typically be in separate files under src/pages/

const LoginPage = () => {
  const { t } = useTranslation('login');
  const { login } = useAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    try {
      await login({ username, password });
      navigate('/dashboard');
    } catch (err) {
      setError(t('loginFailed'));
    }
  };

  return (
    <Container component="main" maxWidth="xs" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
      <Typography component="h1" variant="h5">{t('loginTitle')}</Typography>
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
        <TextField
          margin="normal"
          required
          fullWidth
          id="username"
          label={t('usernameLabel')}
          name="username"
          autoComplete="username"
          autoFocus
          value={username}
          onChange={(e) => setUsername(e.target.value)}
        />
        <TextField
          margin="normal"
          required
          fullWidth
          name="password"
          label={t('passwordLabel')}
          type="password"
          id="password"
          autoComplete="current-password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        {error && <Typography color="error">{error}</Typography>}
        <Button type="submit" fullWidth variant="contained" sx={{ mt: 3, mb: 2 }} startIcon={<LoginIcon />}>
          {t('signInButton')}
        </Button>
      </Box>
    </Container>
  );
};

const DashboardPage = () => {
  const { t } = useTranslation('dashboard');
  return <Typography variant="h4">{t('dashboardTitle')}</Typography>;
};
const StudentsPage = () => {
  const { t } = useTranslation('common');
  return <Typography variant="h4">{t('students')}</Typography>;
};
const ReportsPage = () => {
  const { t } = useTranslation('common');
  return <Typography variant="h4">{t('reports')}</Typography>;
};
const SettingsPage = () => {
  const { t } = useTranslation('common');
  return <Typography variant="h4">{t('settings')}</Typography>;
};
const NotFoundPage = () => {
  const { t } = useTranslation('common');
  return (
    <Box sx={{ textAlign: 'center', mt: 5 }}>
      <Typography variant="h3">{t('notFoundTitle')}</Typography>
      <Typography>{t('notFoundMessage')}</Typography>
      <Button component={Link} to="/dashboard" variant="contained" sx={{ mt: 2 }}>
        {t('goHome')}
      </Button>
    </Box>
  );
};
const LoadingSpinner = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
    <CircularProgress />
  </Box>
);


// --- 6. PROTECTED ROUTE COMPONENT ---
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuth();
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  return children;
};


// --- 7. MAIN APP COMPONENT & ROUTER CONFIGURATION ---
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <AppSettingsProvider> {/* AppSettingsProvider wraps I18nextProvider or vice versa, depending on needs */}
          {/* Suspense for i18next loading translations */}
          <Suspense fallback={<LoadingSpinner />}>
            <BrowserRouter>
              <Routes>
                <Route path="/login" element={<LoginPage />} />
                <Route 
                  path="/" 
                  element={
                    <ProtectedRoute>
                      <MainLayout />
                    </ProtectedRoute>
                  }
                >
                  {/* Nested routes within MainLayout */}
                  <Route index element={<Navigate to="/dashboard" replace />} /> {/* Default to dashboard */}
                  <Route path="dashboard" element={<DashboardPage />} />
                  <Route path="students" element={<StudentsPage />} />
                  {/* Add route for individual student profile e.g., /students/:studentId */}
                  <Route path="reports" element={<ReportsPage />} />
                  <Route path="settings" element={<SettingsPage />} />
                </Route>
                <Route path="*" element={<NotFoundPage />} /> {/* Catch-all for 404 */}
              </Routes>
            </BrowserRouter>
          </Suspense>
        </AppSettingsProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
