import os
import logging
from typing import List, Optional, Dict, Any
import json

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.security import APIKeyHeader # If we were to use a dedicated API key for this service
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
import uvicorn
import openai

# Load environment variables from .env file (especially for local development)
load_dotenv()

# --- Logging Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- FastAPI App Initialization ---
app = FastAPI(
    title="Student SWOT Analysis AI Service",
    description="AI service for generating SWOT analysis for students in the Indian education context.",
    version="1.0.0"
)

# --- OpenAI Client Initialization ---
# The API key is expected to be passed in each request as per ai_api_documentation.md.
# If a platform-wide key were used, it would be initialized here from an environment variable.
# Example: client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY_PLATFORM"))
# For now, the client will be initialized per request with the user-provided key.

# --- Pydantic Models for API Request and Response ---

class StudentInfoInput(BaseModel):
    name: str
    standard: str = Field(..., description="e.g., '10', 'Class 10', 'XII'")
    section: Optional[str] = None
    board: str = Field(..., description="e.g., 'CBSE', 'ICSE', 'State_KA'")
    medium: Optional[str] = Field("English", description="e.g., 'English', 'Hindi'")

class AcademicSubjectInput(BaseModel):
    subject_name: str
    marks_obtained: float
    total_marks: float
    percentage: Optional[float] = None
    grade: Optional[str] = None
    rank_in_class: Optional[int] = None
    teacher_remarks: Optional[str] = None

    @validator('percentage', always=True)
    def calculate_percentage(cls, v, values):
        if v is None and 'marks_obtained' in values and 'total_marks' in values and values['total_marks'] > 0:
            return round((values['marks_obtained'] / values['total_marks']) * 100, 2)
        return v

class AcademicPerformanceInput(BaseModel):
    term_name: str = Field(..., description="e.g., 'Term 1', 'Half Yearly', 'Annual'")
    exam_type: Optional[str] = Field("Summative", description="e.g., 'Formative', 'Summative', 'Unit Test'")
    subjects: List[AcademicSubjectInput]
    overall_percentage: Optional[float] = None
    overall_grade: Optional[str] = None
    overall_rank_in_class: Optional[int] = None

    @validator('overall_percentage', always=True)
    def calculate_overall_percentage(cls, v, values):
        if v is None and 'subjects' in values and values['subjects']:
            total_marks_obtained = sum(s.marks_obtained for s in values['subjects'])
            total_possible_marks = sum(s.total_marks for s in values['subjects'])
            if total_possible_marks > 0:
                return round((total_marks_obtained / total_possible_marks) * 100, 2)
        return v

class AttendanceInput(BaseModel):
    term_attendance_percentage: Optional[float] = None
    total_present_days: Optional[int] = None
    total_absent_days: Optional[int] = None
    late_arrivals: Optional[int] = 0

class BehaviorIncidentInput(BaseModel):
    description: str
    nature: str = Field(..., pattern="^(Positive|Negative|Neutral)$")
    category: Optional[str] = None
    date: Optional[str] = None # YYYY-MM-DD

class CoScholasticGradeInput(BaseModel):
    area: str # e.g., "Life Skills - Thinking", "Attitude - Teachers"
    grade: str # e.g., "A+", "A", "B"

class BehaviorInput(BaseModel):
    discipline_incidents: Optional[List[BehaviorIncidentInput]] = []
    co_scholastic_grades: Optional[List[CoScholasticGradeInput]] = []
    teacher_remarks_on_conduct: Optional[str] = None

class ExtracurricularActivityInput(BaseModel):
    name: str
    category: str # e.g., "Sports", "Cultural", "Technical"
    role: Optional[str] = "Participant"
    level: Optional[str] = Field("School", description="e.g., 'School', 'Zonal', 'State'")
    achievement: Optional[str] = None

class ExtracurricularInput(BaseModel):
    activities: Optional[List[ExtracurricularActivityInput]] = []

class StudentDataForSWOT(BaseModel):
    student_id: str
    academic_term_id: str
    api_key: str = Field(..., description="User's OpenAI API Key. Caution: Sending API keys in requests is generally not recommended for production systems. Consider server-side key management.")
    student_info: StudentInfoInput
    academic_performance: List[AcademicPerformanceInput] # List to potentially include multiple terms for trend context
    attendance: Optional[AttendanceInput] = None
    behavior: Optional[BehaviorInput] = None
    extracurricular: Optional[ExtracurricularInput] = None
    additional_context: Optional[str] = Field(None, description="Any other specific notes from teachers or previous observations.")

class SWOTItem(BaseModel):
    description: str
    category: str = Field(..., description="e.g., Academic, Behavioral, Extracurricular, Social, Personal")
    evidence: Optional[List[str]] = Field(None, description="Data points or observations supporting this item")

class SWOTAnalysisOutput(BaseModel):
    strengths: List[SWOTItem]
    weaknesses: List[SWOTItem]
    opportunities: List[SWOTItem]
    threats: List[SWOTItem]

class RecommendationItem(BaseModel):
    description: str
    target_audience: str = Field(..., pattern="^(Student|Parent|Teacher)$", description="To whom this recommendation is primarily addressed")
    category: str = Field(..., description="e.g., Academic Improvement, Skill Development, Behavioral Support, Extracurricular Engagement")
    priority: str = Field("Medium", pattern="^(High|Medium|Low)$")
    action_items: Optional[List[str]] = None

class IndiaSpecificInsightsOutput(BaseModel):
    competitive_exam_readiness: Optional[Dict[str, Any]] = Field(None, description="e.g. {'jee': {'readiness': 'Moderate', 'suggestions': ['Focus on Physics numericals']}, 'neet': {'readiness': 'Low', 'suggestions': ['Strengthen Biology concepts']}}")
    stream_suitability: Optional[Dict[str, Any]] = Field(None, description="e.g. {'science': {'suitability': 'High', 'remarks': 'Strong in Math & Science'}, 'commerce': {'suitability': 'Medium', 'remarks': 'Consider if interest in economics develops'}}")
    other_observations: Optional[str] = None

class AISWOTGenerationResponse(BaseModel):
    student_id: str
    academic_term_id: str
    swot: SWOTAnalysisOutput
    recommendations: List[RecommendationItem]
    india_specific_insights: Optional[IndiaSpecificInsightsOutput] = None
    generated_at: str
    model_used: Optional[str] = None
    usage_tokens: Optional[Dict[str, int]] = None


# --- Helper Functions ---

def format_student_data_for_prompt(data: StudentDataForSWOT) -> str:
    """
    Formats the structured student data into a comprehensive text prompt for OpenAI.
    """
    prompt_parts = []

    # Student Info
    info = data.student_info
    prompt_parts.append(f"Student Name: {info.name}")
    prompt_parts.append(f"Class/Standard: {info.standard}{f', Section: {info.section}' if info.section else ''}")
    prompt_parts.append(f"Board: {info.board}, Medium: {info.medium}")
    prompt_parts.append("\n---")

    # Academic Performance
    prompt_parts.append("Academic Performance:")
    for perf in data.academic_performance:
        prompt_parts.append(f"  Term: {perf.term_name} (Exam Type: {perf.exam_type})")
        if perf.overall_percentage is not None:
            prompt_parts.append(f"    Overall: {perf.overall_percentage}%" + 
                                (f", Grade: {perf.overall_grade}" if perf.overall_grade else "") +
                                (f", Rank: {perf.overall_rank_in_class}" if perf.overall_rank_in_class else ""))
        prompt_parts.append("    Subjects:")
        for sub in perf.subjects:
            subject_line = f"      - {sub.subject_name}: {sub.marks_obtained}/{sub.total_marks}"
            if sub.percentage is not None:
                subject_line += f" ({sub.percentage}%)"
            if sub.grade:
                subject_line += f", Grade: {sub.grade}"
            if sub.rank_in_class:
                subject_line += f", Rank: {sub.rank_in_class}"
            if sub.teacher_remarks:
                subject_line += f", Remarks: \"{sub.teacher_remarks}\""
            prompt_parts.append(subject_line)
    prompt_parts.append("\n---")

    # Attendance
    if data.attendance:
        att = data.attendance
        prompt_parts.append("Attendance:")
        if att.term_attendance_percentage is not None:
             prompt_parts.append(f"  Term Attendance: {att.term_attendance_percentage}%")
        if att.total_present_days is not None and att.total_absent_days is not None:
            prompt_parts.append(f"  Present: {att.total_present_days} days, Absent: {att.total_absent_days} days")
        if att.late_arrivals is not None:
            prompt_parts.append(f"  Late Arrivals: {att.late_arrivals}")
        prompt_parts.append("\n---")

    # Behavior
    if data.behavior:
        beh = data.behavior
        prompt_parts.append("Behavior:")
        if beh.discipline_incidents:
            prompt_parts.append("  Discipline Incidents:")
            for inc in beh.discipline_incidents:
                prompt_parts.append(f"    - ({inc.nature}) {inc.description}" + (f" on {inc.date}" if inc.date else ""))
        if beh.co_scholastic_grades:
            prompt_parts.append("  Co-Scholastic Grades:")
            for cs_grade in beh.co_scholastic_grades:
                prompt_parts.append(f"    - {cs_grade.area}: {cs_grade.grade}")
        if beh.teacher_remarks_on_conduct:
            prompt_parts.append(f"  Teacher Remarks on Conduct: \"{beh.teacher_remarks_on_conduct}\"")
        prompt_parts.append("\n---")

    # Extracurricular
    if data.extracurricular and data.extracurricular.activities:
        prompt_parts.append("Extracurricular Activities:")
        for act in data.extracurricular.activities:
            activity_line = f"  - {act.name} ({act.category})"
            if act.role: activity_line += f", Role: {act.role}"
            if act.level: activity_line += f", Level: {act.level}"
            if act.achievement: activity_line += f", Achievement: \"{act.achievement}\""
            prompt_parts.append(activity_line)
        prompt_parts.append("\n---")
    
    # Additional Context
    if data.additional_context:
        prompt_parts.append("Additional Context/Observations:")
        prompt_parts.append(f"  {data.additional_context}")
        prompt_parts.append("\n---")

    return "\n".join(prompt_parts)

async def call_openai_api(api_key: str, student_data_prompt: str, model_name: str = "gpt-4o-mini") -> Dict[str, Any]:
    """
    Calls the OpenAI API to generate SWOT analysis.
    """
    client = openai.AsyncOpenAI(api_key=api_key)
    
    system_prompt = """
You are an expert AI assistant specializing in student performance analysis for the Indian education system.
Your task is to generate a comprehensive SWOT analysis (Strengths, Weaknesses, Opportunities, Threats),
actionable recommendations, and India-specific insights (like competitive exam readiness and stream suitability)
based on the provided student data.

Consider the context of Indian education:
- Importance of board exams (CBSE, ICSE, State Boards).
- Relevance of competitive exams (JEE, NEET, CLAT, Olympiads, NTSE etc.).
- Stream selection (Science, Commerce, Arts) for classes 11-12.
- Co-scholastic areas and their impact.
- Cultural nuances in behavior and extracurriculars.

Output Format:
Provide your response strictly as a JSON object with the following structure:
{
  "swot": {
    "strengths": [{"description": "...", "category": "Academic/Behavioral/Extracurricular/Social/Personal", "evidence": ["supporting data point 1", "..."]}, ...],
    "weaknesses": [{"description": "...", "category": "...", "evidence": ["..."]}, ...],
    "opportunities": [{"description": "...", "category": "...", "evidence": ["..."]}, ...],
    "threats": [{"description": "...", "category": "...", "evidence": ["..."]}, ...]
  },
  "recommendations": [
    {"description": "...", "target_audience": "Student/Parent/Teacher", "category": "Academic Improvement/Skill Development/Behavioral Support/Extracurricular Engagement", "priority": "High/Medium/Low", "action_items": ["action 1", "..."]},
    ...
  ],
  "india_specific_insights": {
    "competitive_exam_readiness": {"exam_name1 (e.g. JEE)": {"readiness": "High/Medium/Low/Not Applicable", "suggestions": ["suggestion 1", "..."]}, ...},
    "stream_suitability": {"stream_name1 (e.g. Science)": {"suitability": "High/Medium/Low/Not Recommended", "remarks": "..."}, ...},
    "other_observations": "Any other relevant observations for the Indian context."
  }
}

Ensure descriptions are concise yet insightful. Evidence should be specific data points from the input.
Recommendations should be actionable.
Categories for SWOT and recommendations should be chosen from the provided examples or similar relevant terms.
Priorities for recommendations should be High, Medium, or Low.
Target audience for recommendations should be Student, Parent, or Teacher.
"""

    try:
        logger.info(f"Calling OpenAI API with model: {model_name}")
        response = await client.chat.completions.create(
            model=model_name,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": student_data_prompt}
            ],
            temperature=0.5, # Lower temperature for more focused and deterministic output
            max_tokens=2000  # Adjust as needed, ensure it's enough for comprehensive analysis
        )
        
        raw_response_content = response.choices[0].message.content
        if not raw_response_content:
            raise ValueError("OpenAI returned an empty response.")
            
        parsed_response = json.loads(raw_response_content)
        
        usage = response.usage
        token_details = {"prompt_tokens": usage.prompt_tokens, "completion_tokens": usage.completion_tokens, "total_tokens": usage.total_tokens} if usage else None

        logger.info(f"OpenAI API call successful. Tokens used: {token_details}")
        return parsed_response, token_details

    except openai.APIConnectionError as e:
        logger.error(f"OpenAI API connection error: {e}")
        raise HTTPException(status_code=503, detail=f"OpenAI API connection error: {e}")
    except openai.RateLimitError as e:
        logger.error(f"OpenAI API rate limit exceeded: {e}")
        raise HTTPException(status_code=429, detail=f"OpenAI API rate limit exceeded: {e}")
    except openai.AuthenticationError as e:
        logger.error(f"OpenAI API authentication error (invalid API key?): {e}")
        raise HTTPException(status_code=401, detail=f"OpenAI API authentication error: {e}. Check your API key.")
    except openai.APIStatusError as e: # Catch other OpenAI API errors
        logger.error(f"OpenAI API status error: {e.status_code} - {e.response}")
        raise HTTPException(status_code=e.status_code, detail=f"OpenAI API error: {e.message}")
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse OpenAI JSON response: {e}. Raw response: {raw_response_content}")
        raise HTTPException(status_code=500, detail="Error parsing AI response. The AI may have returned an invalid JSON.")
    except ValueError as e:
        logger.error(f"Value error during OpenAI processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"An unexpected error occurred while calling OpenAI: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred with the AI service: {e}")

def parse_openai_response(openai_json: Dict[str, Any], student_id: str, academic_term_id: str, model_used: str, token_details: Optional[Dict[str,int]]) -> AISWOTGenerationResponse:
    """
    Parses the JSON output from OpenAI and maps it to the AISWOTGenerationResponse model.
    This function adds a layer of validation and transformation if needed.
    """
    try:
        # Directly use Pydantic for validation if structure matches
        # Add transformations here if OpenAI's output structure differs slightly
        # or if defaults/fallbacks are needed.
        
        # Ensure all main keys exist
        if not all(k in openai_json for k in ["swot", "recommendations"]):
            logger.error(f"OpenAI response missing critical keys 'swot' or 'recommendations'. Response: {openai_json}")
            raise ValueError("OpenAI response is missing critical keys.")

        # Validate SWOT structure
        swot_data = openai_json.get("swot", {})
        if not all(k in swot_data for k in ["strengths", "weaknesses", "opportunities", "threats"]):
            logger.error(f"SWOT data from OpenAI is malformed. Response: {swot_data}")
            raise ValueError("SWOT data from AI is malformed.")

        # Simple example of ensuring lists are present even if empty from AI
        swot_output = SWOTAnalysisOutput(
            strengths=[SWOTItem(**s) for s in swot_data.get("strengths", [])],
            weaknesses=[SWOTItem(**w) for w in swot_data.get("weaknesses", [])],
            opportunities=[SWOTItem(**o) for o in swot_data.get("opportunities", [])],
            threats=[SWOTItem(**t) for t in swot_data.get("threats", [])]
        )

        recommendations_output = [RecommendationItem(**r) for r in openai_json.get("recommendations", [])]
        
        india_specific_insights_data = openai_json.get("india_specific_insights")
        india_specific_output = IndiaSpecificInsightsOutput(**india_specific_insights_data) if india_specific_insights_data else None

        return AISWOTGenerationResponse(
            student_id=student_id,
            academic_term_id=academic_term_id,
            swot=swot_output,
            recommendations=recommendations_output,
            india_specific_insights=india_specific_output,
            generated_at=new_request.created_at.isoformat() if hasattr(new_request, 'created_at') else datetime.utcnow().isoformat(), # Placeholder for actual request time
            model_used=model_used,
            usage_tokens=token_details
        )
    except Exception as e:
        logger.error(f"Error parsing OpenAI response into Pydantic models: {e}. Original response: {openai_json}")
        raise ValueError(f"Error processing AI response structure: {e}")


# --- API Endpoints ---

@app.post("/generate-swot/", response_model=AISWOTGenerationResponse)
async def generate_swot_analysis_endpoint(student_data: StudentDataForSWOT, request: Request):
    """
    Generates a comprehensive SWOT analysis for a student using AI.
    This includes strengths, weaknesses, opportunities, threats, actionable recommendations,
    and India-specific insights like competitive exam readiness and stream suitability.

    The user's OpenAI API key must be provided in the request body.
    **Security Note**: Passing API keys in client requests is generally discouraged for production systems.
    Consider a model where the platform manages the OpenAI API key securely on the server-side.
    """
    logger.info(f"Received SWOT generation request for student_id: {student_data.student_id}, term_id: {student_data.academic_term_id}")

    if not student_data.api_key:
        logger.error("OpenAI API key missing in request.")
        raise HTTPException(status_code=400, detail="OpenAI API key is required.")

    # 1. Format student data into a prompt
    try:
        formatted_prompt = format_student_data_for_prompt(student_data)
        logger.debug(f"Formatted prompt for OpenAI:\n{formatted_prompt}")
    except Exception as e:
        logger.error(f"Error formatting student data for prompt: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing student data: {e}")

    # 2. Call OpenAI API
    # One could make the model configurable via request or env var
    # For now, using gpt-4o-mini as it's cost-effective and supports JSON mode
    model_to_use = "gpt-4o-mini" 
    try:
        openai_response_json, token_details = await call_openai_api(student_data.api_key, formatted_prompt, model_name=model_to_use)
        logger.debug(f"Raw OpenAI response: {openai_response_json}")
    except HTTPException as e: # Re-raise HTTPException from call_openai_api
        raise e
    except Exception as e:
        logger.error(f"Unexpected error calling OpenAI API: {e}")
        raise HTTPException(status_code=500, detail=f"AI service communication failed: {e}")

    # 3. Parse OpenAI response and map to output model
    try:
        # This is a placeholder for the actual request time.
        # In a real system, you might log the request time when it's received.
        from datetime import datetime, timezone
        global new_request # This is not ideal, just for placeholder
        new_request = BaseModel() # Mocking a request object
        new_request.created_at = datetime.now(timezone.utc)

        parsed_response = parse_openai_response(
            openai_json=openai_response_json,
            student_id=student_data.student_id,
            academic_term_id=student_data.academic_term_id,
            model_used=model_to_use,
            token_details=token_details
        )
    except ValueError as e: # Catch parsing errors from parse_openai_response
        logger.error(f"Error parsing or validating OpenAI response: {e}")
        raise HTTPException(status_code=500, detail=f"Invalid response structure from AI: {e}")
    except Exception as e:
        logger.error(f"Unexpected error processing AI response: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process AI response: {e}")

    # TODO: Store the generated SWOT analysis in the database (e.g., via a call to the main API server or direct DB access if this service has it)
    # This is a critical step not detailed here as this service is focused on AI generation.
    logger.info(f"Successfully generated SWOT for student {student_data.student_id}. Storing analysis is the next step for the calling service.")

    return parsed_response


@app.get("/")
async def root():
    """
    Root endpoint for health check.
    """
    logger.info("Health check endpoint was called.")
    return {"status": "AI Service is running", "version": app.version}

# --- Main Execution Block ---
if __name__ == "__main__":
    # This is for local development.
    # For production, use a Gunicorn or similar ASGI server.
    # Example: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    
    # Note on "training" mentioned by user:
    # True AI model "training" (fine-tuning) is a more complex process involving preparing datasets
    # and using OpenAI's fine-tuning APIs or training custom models.
    # This service currently relies on "prompt engineering" with powerful general models.
    # For even better, more tailored results, fine-tuning or few-shot prompting with curated examples
    # could be explored as future enhancements.

    port = int(os.getenv("PORT", 8000)) # Default to 8000 if PORT not set
    logger.info(f"Starting AI service on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)

# Example of how to run this service:
# 1. Save this file as main.py in a directory (e.g., ai-service).
# 2. Create a .env file in the same directory with your OPENAI_API_KEY_PLATFORM if you decide to use a platform key.
#    (Currently, the service expects API key in request).
# 3. Install dependencies: pip install fastapi uvicorn python-dotenv openai pydantic
# 4. Run: uvicorn main:app --reload
#
# Then you can send POST requests to http://localhost:8000/generate-swot/
# with a JSON body matching the StudentDataForSWOT model.
