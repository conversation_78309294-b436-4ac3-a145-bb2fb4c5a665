/**
 * Student SWOT Analysis Platform - Database Schema
 * 
 * This file defines the MongoDB schema using Mongoose for the SWOT Analysis Platform
 * specifically adapted for Indian schools. It includes all entity models with proper
 * validation, relationships, and India-specific fields.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * School Schema
 * Represents an educational institution in the Indian context
 * Includes fields for board affiliation, medium of instruction, etc.
 */
const SchoolSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  code: { 
    type: String, 
    required: true,
    unique: true,
    trim: true,
    // UDISE code in India
    validate: {
      validator: function(v) {
        return /^\d{11}$/.test(v);
      },
      message: props => `${props.value} is not a valid UDISE code!`
    }
  },
  branch: { 
    type: String, 
    trim: true 
  },
  affiliation: {
    board: { 
      type: String, 
      required: true,
      enum: ['CBSE', 'ICSE', 'State_AP', 'State_TS', 'State_KA', 'State_TN', 'State_KL', 
             'State_MH', 'State_GJ', 'State_MP', 'State_UP', 'State_WB', 'State_BR', 
             'State_OR', 'State_PB', 'State_HR', 'State_RJ', 'State_JK', 'State_UK', 
             'State_HP', 'State_AS', 'State_MN', 'State_ML', 'State_TR', 'State_AR', 
             'State_NL', 'State_MZ', 'State_SK', 'State_GA', 'State_DL', 'IB', 'Cambridge', 'Other']
    },
    number: { 
      type: String, 
      trim: true 
    },
    validity: { 
      type: Date 
    }
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, required: true, trim: true },
    district: { type: String, required: true, trim: true },
    state: { type: String, required: true, trim: true },
    pincode: { 
      type: String, 
      required: true,
      validate: {
        validator: function(v) {
          return /^\d{6}$/.test(v);
        },
        message: props => `${props.value} is not a valid Indian PIN code!`
      }
    },
    zone: { type: String, trim: true } // Educational zone
  },
  contact: {
    email: { 
      type: String, 
      required: true,
      trim: true,
      lowercase: true,
      validate: {
        validator: function(v) {
          return /^\S+@\S+\.\S+$/.test(v);
        },
        message: props => `${props.value} is not a valid email address!`
      }
    },
    phone: { 
      type: String, 
      required: true,
      validate: {
        validator: function(v) {
          return /^[6-9]\d{9}$/.test(v);
        },
        message: props => `${props.value} is not a valid Indian phone number!`
      }
    },
    alternative_phone: { type: String },
    website: { type: String, trim: true }
  },
  administration: {
    principal_name: { type: String, trim: true },
    manager_name: { type: String, trim: true },
    trust_name: { type: String, trim: true }
  },
  classification: {
    type: { 
      type: String, 
      enum: ['Government', 'Private', 'Aided', 'KV', 'JNV', 'Sainik', 'Other'],
      required: true
    },
    category: { 
      type: String, 
      enum: ['Primary', 'Upper Primary', 'Secondary', 'Higher Secondary'],
      required: true
    },
    gender: { 
      type: String, 
      enum: ['Co-Ed', 'Boys', 'Girls'],
      required: true
    },
    medium: [{ 
      type: String,
      enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada', 'Malayalam', 'Marathi', 
             'Gujarati', 'Bengali', 'Odia', 'Punjabi', 'Assamese', 'Urdu', 'Other']
    }],
    residential: { type: Boolean, default: false },
    religious_affiliation: { type: String, trim: true }
  },
  subscription: {
    tier: { 
      type: String, 
      enum: ['Lite', 'Standard', 'Premium', 'Elite', 'Enterprise'],
      default: 'Lite'
    },
    features_enabled: [String],
    payment_status: { 
      type: String, 
      enum: ['Active', 'Pending', 'Overdue', 'Cancelled'],
      default: 'Active'
    },
    renewal_date: { type: Date }
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Academic Year Schema
 * Represents an academic year in the Indian context
 * Indian schools typically follow April-March academic year
 */
const AcademicYearSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  name: { 
    type: String, 
    required: true,
    trim: true,
    // Format: YYYY-YYYY (e.g., 2024-2025)
    validate: {
      validator: function(v) {
        return /^\d{4}-\d{4}$/.test(v);
      },
      message: props => `${props.value} is not a valid academic year format!`
    }
  },
  start_date: { 
    type: Date, 
    required: true 
  },
  end_date: { 
    type: Date, 
    required: true 
  },
  is_current: { 
    type: Boolean, 
    default: false 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Academic Term Schema
 * Represents an academic term in the Indian context
 * Indian schools typically have 2-3 terms per academic year instead of quarters
 */
const AcademicTermSchema = new Schema({
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  name: { 
    type: String, 
    required: true,
    trim: true,
    // E.g., "Term 1", "First Term", "Half Yearly", "Annual"
  },
  term_number: { 
    type: Number, 
    required: true,
    min: 1,
    max: 3
  },
  start_date: { 
    type: Date, 
    required: true 
  },
  end_date: { 
    type: Date, 
    required: true 
  },
  examination_dates: {
    start: { type: Date },
    end: { type: Date }
  },
  is_current: { 
    type: Boolean, 
    default: false 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Class Schema
 * Represents a class/standard in the Indian context
 * Includes sections and stream information
 */
const ClassSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  standard: { 
    type: Number, 
    required: true,
    min: 1,
    max: 12
  },
  section: { 
    type: String, 
    required: true,
    trim: true
  },
  stream: { 
    type: String, 
    enum: ['N/A', 'Science', 'Commerce', 'Arts', 'Vocational'],
    default: 'N/A'
  },
  class_teacher_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Teacher' 
  },
  room_number: { 
    type: String, 
    trim: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Student Schema
 * Represents a student in the Indian context
 * Includes India-specific fields like admission number, roll number, caste category, etc.
 */
const StudentSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  admission_number: { 
    type: String, 
    required: true,
    trim: true
  },
  name: {
    first: { 
      type: String, 
      required: true,
      trim: true
    },
    middle: { 
      type: String, 
      trim: true 
    },
    last: { 
      type: String, 
      required: true,
      trim: true
    },
    display: { 
      type: String, 
      trim: true 
      // For names with different display formats
    }
  },
  class: {
    class_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'Class',
      required: true
    },
    roll_number: { 
      type: Number, 
      required: true
    }
  },
  gender: { 
    type: String, 
    required: true,
    enum: ['Male', 'Female', 'Other']
  },
  date_of_birth: { 
    type: Date, 
    required: true 
  },
  category: {
    type: { 
      type: String, 
      enum: ['General', 'SC', 'ST', 'OBC', 'EWS', 'Other'],
      default: 'General'
    },
    certificate: { 
      type: String, 
      trim: true 
      // Reference to certificate ID if applicable
    }
  },
  contact: {
    address: { type: String, trim: true },
    city: { type: String, trim: true },
    district: { type: String, trim: true },
    state: { type: String, trim: true },
    pincode: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true, lowercase: true }
  },
  board: { 
    type: String, 
    enum: ['CBSE', 'ICSE', 'State', 'IB', 'Cambridge', 'Other'],
    required: true
  },
  medium: { 
    type: String, 
    enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada', 'Malayalam', 'Marathi', 
           'Gujarati', 'Bengali', 'Odia', 'Punjabi', 'Assamese', 'Urdu', 'Other'],
    required: true
  },
  religion: { 
    type: String, 
    enum: ['Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Other', 'Not Specified'],
    default: 'Not Specified'
  },
  nationality: { 
    type: String, 
    default: 'Indian'
  },
  blood_group: { 
    type: String, 
    enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown'],
    default: 'Unknown'
  },
  aadhar_number: { 
    type: String,
    trim: true,
    // Encrypted/masked in actual implementation
    validate: {
      validator: function(v) {
        return !v || /^\d{12}$/.test(v);
      },
      message: props => `${props.value} is not a valid Aadhar number!`
    }
  },
  father_name: { 
    type: String, 
    trim: true 
  },
  mother_name: { 
    type: String, 
    trim: true 
  },
  guardian: {
    name: { type: String, trim: true },
    relationship: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true, lowercase: true },
    occupation: { type: String, trim: true }
  },
  is_active: { 
    type: Boolean, 
    default: true 
  },
  admission_date: { 
    type: Date 
  },
  profile_image: { 
    type: String, 
    trim: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Academic Performance Schema
 * Represents academic performance in the Indian context
 * Adapted for Indian grading systems and term structure
 */
const AcademicPerformanceSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  term: {
    term_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'AcademicTerm',
      required: true
    },
    number: { 
      type: Number, 
      required: true,
      min: 1,
      max: 3
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  exam_type: {
    category: { 
      type: String, 
      enum: ['Formative', 'Summative', 'Unit Test', 'Pre-Board', 'Final', 'Other'],
      required: true
    },
    weightage: { 
      type: Number, 
      min: 0,
      max: 100,
      default: 100
    }
  },
  subjects: [{
    subject_name: { 
      type: String, 
      required: true,
      trim: true
    },
    subject_code: { 
      type: String, 
      trim: true 
    },
    marks_obtained: { 
      type: Number, 
      required: true,
      min: 0
    },
    total_marks: { 
      type: Number, 
      required: true,
      min: 0
    },
    percentage: { 
      type: Number, 
      min: 0,
      max: 100
    },
    grade: { 
      type: String, 
      trim: true 
    },
    grade_point: { 
      type: Number, 
      min: 0,
      max: 10 
    },
    practical: {
      marks_obtained: { type: Number, min: 0 },
      total_marks: { type: Number, min: 0 }
    },
    theory: {
      marks_obtained: { type: Number, min: 0 },
      total_marks: { type: Number, min: 0 }
    },
    internal_assessment: {
      marks_obtained: { type: Number, min: 0 },
      total_marks: { type: Number, min: 0 }
    },
    rank_in_class: { 
      type: Number, 
      min: 1 
    },
    teacher_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'Teacher' 
    },
    teacher_remarks: { 
      type: String 
    }
  }],
  overall_results: {
    total_marks_obtained: { 
      type: Number, 
      min: 0 
    },
    total_possible_marks: { 
      type: Number, 
      min: 0 
    },
    percentage: { 
      type: Number, 
      min: 0,
      max: 100
    },
    grade: { 
      type: String, 
      trim: true 
    },
    grade_point: { 
      type: Number, 
      min: 0,
      max: 10
    },
    division: { 
      type: String, 
      enum: ['First', 'Second', 'Third', 'Fail', 'N/A'],
      default: 'N/A'
    },
    result: { 
      type: String, 
      enum: ['Pass', 'Fail', 'Compartment', 'Absent', 'N/A'],
      default: 'N/A'
    },
    rank_in_class: { 
      type: Number, 
      min: 1 
    },
    rank_in_section: { 
      type: Number, 
      min: 1 
    }
  },
  previous_term_comparison: {
    percentage_change: { type: Number },
    rank_change: { type: Number },
    grade_change: { type: String }
  },
  scholastic_areas: [{
    area: { type: String, trim: true },
    grade: { type: String, trim: true },
    remarks: { type: String }
  }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Attendance Schema
 * Represents attendance records in the Indian context
 * Includes monthly records and term summary
 */
const AttendanceSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  term: {
    term_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'AcademicTerm',
      required: true
    },
    number: { 
      type: Number, 
      required: true,
      min: 1,
      max: 3
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  monthly_records: [{
    month: { 
      type: String, 
      required: true,
      enum: ['April', 'May', 'June', 'July', 'August', 'September', 
             'October', 'November', 'December', 'January', 'February', 'March']
    },
    working_days: { 
      type: Number, 
      required: true,
      min: 0
    },
    present_days: { 
      type: Number, 
      required: true,
      min: 0
    },
    absent_days: { 
      type: Number, 
      required: true,
      min: 0
    },
    leave: {
      medical: { type: Number, default: 0, min: 0 },
      pre_approved: { type: Number, default: 0, min: 0 },
      unauthorized: { type: Number, default: 0, min: 0 }
    },
    late_arrivals: { 
      type: Number, 
      default: 0,
      min: 0
    },
    attendance_percentage: { 
      type: Number, 
      min: 0,
      max: 100
    }
  }],
  term_summary: {
    total_working_days: { 
      type: Number, 
      required: true,
      min: 0
    },
    total_present: { 
      type: Number, 
      required: true,
      min: 0
    },
    total_absent: { 
      type: Number, 
      required: true,
      min: 0
    },
    total_leaves: {
      medical: { type: Number, default: 0, min: 0 },
      pre_approved: { type: Number, default: 0, min: 0 },
      unauthorized: { type: Number, default: 0, min: 0 }
    },
    total_late_arrivals: { 
      type: Number, 
      default: 0,
      min: 0
    },
    attendance_percentage: { 
      type: Number, 
      min: 0,
      max: 100
    }
  },
  school_events_attendance: [{
    event_id: { type: String, trim: true },
    event_name: { type: String, trim: true },
    date: { type: Date },
    attended: { type: Boolean, default: false },
    participation_type: { 
      type: String, 
      enum: ['Attendee', 'Participant', 'Volunteer', 'Organizer'],
      default: 'Attendee'
    }
  }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Daily Attendance Schema
 * Represents individual daily attendance records
 */
const DailyAttendanceSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  date: { 
    type: Date, 
    required: true 
  },
  status: { 
    type: String, 
    required: true,
    enum: ['Present', 'Absent', 'Late', 'Half Day', 'Leave']
  },
  late_minutes: { 
    type: Number, 
    min: 0,
    default: 0
  },
  leave_type: { 
    type: String, 
    enum: ['Medical', 'Pre-approved', 'Unauthorized', 'N/A'],
    default: 'N/A'
  },
  leave_reason: { 
    type: String, 
    trim: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Behavior Schema
 * Represents behavioral records in the Indian context
 * Includes discipline record and co-scholastic assessment
 */
const BehaviorSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  term: {
    term_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'AcademicTerm',
      required: true
    },
    number: { 
      type: Number, 
      required: true,
      min: 1,
      max: 3
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  discipline_record: [{
    date: { 
      type: Date, 
      required: true 
    },
    nature: { 
      type: String, 
      required: true,
      enum: ['Positive', 'Negative', 'Neutral']
    },
    category: { 
      type: String, 
      trim: true 
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    action_taken: { 
      type: String, 
      trim: true 
    },
    reported_by: { 
      type: String, 
      trim: true 
    },
    parent_notification: { 
      type: Boolean, 
      default: false 
    },
    resolution_status: { 
      type: String, 
      enum: ['Open', 'Resolved', 'In Progress', 'N/A'],
      default: 'N/A'
    }
  }],
  co_scholastic_assessment: {
    life_skills: {
      thinking_skills: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      social_skills: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      emotional_skills: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      }
    },
    attitudes_values: {
      towards_teachers: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      towards_schoolmates: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      towards_environment: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      towards_nation: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      }
    },
    literary_creative_skills: {
      literary: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      creative: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      scientific: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      }
    },
    clubs: {
      type: { 
        type: String, 
        enum: ['Eco', 'Literary', 'Science', 'Math', 'Arts', 'Sports', 'Other']
      },
      grade: { 
        type: String, 
        enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
      },
      remarks: { 
        type: String 
      }
    }
  },
  term_evaluation: {
    deportment: { 
      type: String, 
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
    },
    courtesy: { 
      type: String, 
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
    },
    discipline: { 
      type: String, 
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
    },
    leadership: { 
      type: String, 
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
    },
    responsibility: { 
      type: String, 
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'E']
    },
    teacher_remarks: { 
      type: String 
    }
  },
  improvement_areas: [String],
  strengths_observed: [String],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Extracurricular Activities Schema
 * Represents extracurricular activities in the Indian context
 * Includes sports, cultural activities, competitions, etc.
 */
const ExtracurricularSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  term: {
    term_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'AcademicTerm',
      required: true
    },
    number: { 
      type: Number, 
      required: true,
      min: 1,
      max: 3
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  activities: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Sports', 'Cultural', 'Technical', 'Academic', 'Social', 'Other']
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    },
    role: { 
      type: String, 
      enum: ['Participant', 'Captain', 'Leader', 'Coordinator', 'Other'],
      default: 'Participant'
    },
    level: { 
      type: String, 
      enum: ['School', 'Zonal', 'District', 'State', 'National', 'International'],
      default: 'School'
    },
    position: { 
      type: String, 
      enum: ['First', 'Second', 'Third', 'Participation', 'N/A'],
      default: 'N/A'
    },
    certificate: { 
      type: String, 
      trim: true 
    },
    house: { 
      type: String, 
      trim: true 
      // House system common in Indian schools
    }
  }],
  competitions: [{
    name: { 
      type: String, 
      required: true,
      trim: true
    },
    date: { 
      type: Date, 
      required: true 
    },
    level: { 
      type: String, 
      enum: ['Intramural', 'Interschool', 'District', 'State', 'National', 'International'],
      default: 'Intramural'
    },
    result: { 
      type: String, 
      enum: ['First', 'Second', 'Third', 'Participation', 'N/A'],
      default: 'N/A'
    },
    certificate: { 
      type: String, 
      trim: true 
    }
  }],
  sports: [{
    sport_name: { 
      type: String, 
      required: true,
      trim: true
    },
    team_individual: { 
      type: String, 
      enum: ['Team', 'Individual', 'Both'],
      default: 'Team'
    },
    level_of_participation: { 
      type: String, 
      enum: ['School', 'Zonal', 'District', 'State', 'National', 'International'],
      default: 'School'
    },
    achievement: { 
      type: String, 
      trim: true 
    },
    coach_remarks: { 
      type: String 
    }
  }],
  clubs_and_societies: [{
    name: { 
      type: String, 
      required: true,
      trim: true
    },
    position: { 
      type: String, 
      enum: ['Member', 'Leader', 'Secretary', 'President', 'Other'],
      default: 'Member'
    },
    contribution: { 
      type: String 
    },
    teacher_in_charge: { 
      type: String, 
      trim: true 
    }
  }],
  community_service: [{
    initiative: { 
      type: String, 
      required: true,
      trim: true
    },
    hours_contributed: { 
      type: Number, 
      min: 0
    },
    impact: { 
      type: String 
    }
  }],
  cultural_activities: [{
    activity_type: { 
      type: String, 
      required: true,
      enum: ['Dance', 'Music', 'Drama', 'Art', 'Craft', 'Debate', 'Other']
    },
    performance_details: { 
      type: String 
    },
    level: { 
      type: String, 
      enum: ['School', 'Zonal', 'District', 'State', 'National', 'International'],
      default: 'School'
    }
  }],
  house_activities: {
    house_name: { 
      type: String, 
      trim: true 
    },
    position: { 
      type: String, 
      enum: ['Member', 'Captain', 'Vice-Captain', 'Prefect', 'Other'],
      default: 'Member'
    },
    points_contributed: { 
      type: Number, 
      min: 0
    },
    house_rank: { 
      type: Number, 
      min: 1 
    }
  },
  ncc_nss: {
    enrollment: { 
      type: Boolean, 
      default: false 
    },
    rank: { 
      type: String, 
      trim: true 
    },
    special_achievements: { 
      type: String 
    },
    camps_attended: [String]
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * SWOT Analysis Schema
 * Represents a SWOT analysis in the Indian context
 * Includes strengths, weaknesses, opportunities, threats, and recommendations
 */
const SWOTAnalysisSchema = new Schema({
  student_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  term: {
    term_id: { 
      type: Schema.Types.ObjectId, 
      ref: 'AcademicTerm',
      required: true
    },
    number: { 
      type: Number, 
      required: true,
      min: 1,
      max: 3
    },
    name: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  generated_at: { 
    type: Date, 
    default: Date.now 
  },
  generated_by: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  generation_method: { 
    type: String, 
    enum: ['Automatic', 'Manual', 'AI-Assisted'],
    default: 'Automatic'
  },
  strengths: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Academic', 'Attendance', 'Behavior', 'Extracurricular', 'Overall']
    },
    type: { 
      type: String, 
      required: true,
      trim: true
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    percentile: { 
      type: Number, 
      min: 0,
      max: 100
    },
    rank_context: { 
      type: String, 
      trim: true 
    }
  }],
  weaknesses: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Academic', 'Attendance', 'Behavior', 'Extracurricular', 'Overall']
    },
    type: { 
      type: String, 
      required: true,
      trim: true
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    gap_analysis: {
      current: { type: Number },
      target: { type: Number },
      gap_percentage: { type: Number }
    }
  }],
  opportunities: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Academic', 'Attendance', 'Behavior', 'Extracurricular', 'Overall']
    },
    type: { 
      type: String, 
      required: true,
      trim: true
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    stream_relevance: [String], // Relevance to academic streams
    competitive_exam_relevance: [String] // Relevance to competitive exams
  }],
  threats: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Academic', 'Attendance', 'Behavior', 'Extracurricular', 'Overall']
    },
    type: { 
      type: String, 
      required: true,
      trim: true
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    impact_assessment: { 
      type: String, 
      enum: ['Low', 'Medium', 'High', 'Critical'],
      default: 'Medium'
    },
    timeframe: { 
      type: String, 
      enum: ['Immediate', 'Short-term', 'Long-term'],
      default: 'Short-term'
    }
  }],
  recommendations: [{
    category: { 
      type: String, 
      required: true,
      enum: ['Academic', 'Attendance', 'Behavior', 'Extracurricular', 'Overall']
    },
    description: { 
      type: String, 
      required: true,
      trim: true
    },
    priority: { 
      type: String, 
      enum: ['Low', 'Medium', 'High', 'Critical'],
      default: 'Medium'
    },
    timeline: { 
      type: String, 
      enum: ['Immediate', 'Short-term', 'Long-term'],
      default: 'Short-term'
    },
    specific_resources: [String], // Books, coaching, etc.
    parent_action_items: [String], // What parents should do
    teacher_action_items: [String] // What teachers should do
  }],
  academic_path_analysis: {
    stream_suitability: [{
      stream: { 
        type: String, 
        enum: ['Science', 'Commerce', 'Arts', 'Vocational'],
        required: true
      },
      suitability_score: { 
        type: Number, 
        min: 0,
        max: 100
      },
      rationale: { 
        type: String 
      }
    }],
    career_aptitude: [{
      field: { 
        type: String, 
        required: true,
        trim: true
      },
      aptitude_level: { 
        type: String, 
        enum: ['Low', 'Medium', 'High', 'Very High'],
        default: 'Medium'
      },
      supporting_strengths: [String],
      areas_to_develop: [String]
    }]
  },
  competitive_exam_readiness: [{
    exam_name: { 
      type: String, 
      required: true,
      trim: true
    },
    readiness_score: { 
      type: Number, 
      min: 0,
      max: 100
    },
    key_preparation_areas: [String]
  }],
  visualization_urls: {
    quadrant: { type: String, trim: true },
    academic_radar: { type: String, trim: true },
    trend_chart: { type: String, trim: true },
    comparison_chart: { type: String, trim: true }
  },
  is_shared_with_parent: { 
    type: Boolean, 
    default: false 
  },
  parent_viewed_at: { 
    type: Date 
  },
  teacher_comments: { 
    type: String 
  },
  parent_feedback: { 
    type: String 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Teacher Schema
 * Represents a teacher in the Indian context
 */
const TeacherSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  user_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  employee_id: { 
    type: String, 
    required: true,
    trim: true
  },
  name: {
    first: { 
      type: String, 
      required: true,
      trim: true
    },
    middle: { 
      type: String, 
      trim: true 
    },
    last: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  gender: { 
    type: String, 
    required: true,
    enum: ['Male', 'Female', 'Other']
  },
  date_of_birth: { 
    type: Date 
  },
  contact: {
    address: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    pincode: { type: String, trim: true },
    phone: { 
      type: String, 
      required: true,
      trim: true
    },
    email: { 
      type: String, 
      required: true,
      trim: true,
      lowercase: true
    }
  },
  qualifications: [{
    degree: { type: String, trim: true },
    institution: { type: String, trim: true },
    year: { type: Number },
    specialization: { type: String, trim: true }
  }],
  subjects_taught: [{
    subject: { type: String, trim: true },
    classes: [{ 
      type: Schema.Types.ObjectId, 
      ref: 'Class' 
    }]
  }],
  is_class_teacher: { 
    type: Boolean, 
    default: false 
  },
  class_teacher_of: { 
    type: Schema.Types.ObjectId, 
    ref: 'Class' 
  },
  joining_date: { 
    type: Date 
  },
  is_active: { 
    type: Boolean, 
    default: true 
  },
  profile_image: { 
    type: String, 
    trim: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * User Schema
 * Represents a user of the system (teacher, admin, parent)
 */
const UserSchema = new Schema({
  username: { 
    type: String, 
    required: true,
    unique: true,
    trim: true
  },
  email: { 
    type: String, 
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^\S+@\S+\.\S+$/.test(v);
      },
      message: props => `${props.value} is not a valid email address!`
    }
  },
  password: { 
    type: String, 
    required: true 
    // Will be hashed before saving
  },
  role: { 
    type: String, 
    required: true,
    enum: ['Admin', 'Teacher', 'Parent', 'Staff'],
    default: 'Teacher'
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  profile: {
    name: { type: String, trim: true },
    phone: { type: String, trim: true },
    profile_image: { type: String, trim: true }
  },
  preferences: {
    language: { 
      type: String, 
      enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada', 'Malayalam', 
             'Marathi', 'Gujarati', 'Bengali', 'Odia', 'Punjabi', 'Assamese', 'Urdu'],
      default: 'English'
    },
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: true },
      app: { type: Boolean, default: true }
    },
    theme: { 
      type: String, 
      enum: ['Light', 'Dark', 'System'],
      default: 'System'
    }
  },
  last_login: { 
    type: Date 
  },
  is_active: { 
    type: Boolean, 
    default: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Parent Schema
 * Represents a parent/guardian in the system
 */
const ParentSchema = new Schema({
  user_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  name: {
    first: { 
      type: String, 
      required: true,
      trim: true
    },
    middle: { 
      type: String, 
      trim: true 
    },
    last: { 
      type: String, 
      required: true,
      trim: true
    }
  },
  relationship: { 
    type: String, 
    required: true,
    enum: ['Father', 'Mother', 'Guardian', 'Other']
  },
  contact: {
    address: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    pincode: { type: String, trim: true },
    phone: { 
      type: String, 
      required: true,
      trim: true
    },
    email: { 
      type: String, 
      required: true,
      trim: true,
      lowercase: true
    }
  },
  occupation: { 
    type: String, 
    trim: true 
  },
  children: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'Student' 
  }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

// Create and export models
const models = {
  School: mongoose.model('School', SchoolSchema),
  AcademicYear: mongoose.model('AcademicYear', AcademicYearSchema),
  AcademicTerm: mongoose.model('AcademicTerm', AcademicTermSchema),
  Class: mongoose.model('Class', ClassSchema),
  Student: mongoose.model('Student', StudentSchema),
  AcademicPerformance: mongoose.model('AcademicPerformance', AcademicPerformanceSchema),
  Attendance: mongoose.model('Attendance', AttendanceSchema),
  DailyAttendance: mongoose.model('DailyAttendance', DailyAttendanceSchema),
  Behavior: mongoose.model('Behavior', BehaviorSchema),
  Extracurricular: mongoose.model('Extracurricular', ExtracurricularSchema),
  SWOTAnalysis: mongoose.model('SWOTAnalysis', SWOTAnalysisSchema),
  Teacher: mongoose.model('Teacher', TeacherSchema),
  User: mongoose.model('User', UserSchema),
  Parent: mongoose.model('Parent', ParentSchema)
};

module.exports = models;
