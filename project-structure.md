# Student SWOT Analysis Platform  
## Project Structure Overview

This document describes every directory and key file in the monorepo for the Student SWOT Analysis Platform.  
The goal is to give developers, administrators, and non-technical stakeholders a clear map of where each concern lives—frontend, backend, database, AI service, shared libraries, infrastructure, documentation, and testing.

---

### 1. Top-level Layout

```
student-swot-platform/
├── apps/
│   ├── web-frontend/          # React + Vite application (teacher, parent UIs)
│   ├── api-server/            # Node.js/Express REST & WebSocket server
│   └── ai-service/            # Python FastAPI micro-service for AI/ML
├── packages/
│   ├── data-models/           # TypeScript & Pydantic shared schemas
│   ├── utils/                 # Cross-cutting helper functions
│   └── visualization-kit/     # Reusable Chart.js/D3 React components
├── database/
│   ├── migrations/            # Versioned MongoDB migration scripts
│   ├── seed/                  # Demo & sample data loaders
│   └── schema-diagrams/       # ER diagrams (draw.io / PNG / PlantUML)
├── infra/
│   ├── terraform/             # IaC for MongoDB Atlas, Vercel, AWS (optional)
│   └── vercel/                # vercel.json & build settings
├── docs/                      # Markdown documentation (architecture, API, UX)
├── tests/                     # End-to-end, unit, integration test suites
├── .github/                   # CI/CD workflows, issue templates
├── .env.example               # Example environment variables
├── package.json               # Root workspace management (pnpm/yarn workspaces)
└── README.md                  # High-level overview and quick-start
```

---

### 2. `apps/` – Runtime Applications

| Path | Purpose |
|------|---------|
| **web-frontend/** | Vite + React 18 SPA/PWA with Material UI, i18next (Hindi & regional languages), React Router. Provides dashboards, reports, interactive filtering, authentication screens, offline support. |
| **api-server/** | Node.js 20 + Express REST API. Hosts endpoints for students, teachers, authentication, data import, webhooks, file uploads; connects to MongoDB via Mongoose; exposes Swagger docs. Houses business logic, RBAC, JWT handling. |
| **ai-service/** | Python 3.11 FastAPI micro-service deployed as a Supabase Edge Function/Container. Performs AI SWOT generation, trend detection, recommendation generation using OpenAI & scikit-learn models. Communicates with `api-server` over gRPC/HTTP. Includes ML notebooks and training pipeline. |

---

### 3. `packages/` – Shared Libraries

| Package | Description |
|---------|-------------|
| **data-models/** | Source-of-truth schemas in TypeScript (zod) **and** Python (pydantic). Ensures consistency between frontend, backend, and AI service. Includes validation helpers and board-specific grade mappings (CBSE, ICSE, State). |
| **utils/** | Pure utility functions (logging wrapper, date helpers, localization helpers, common constants). Published internally as a versioned package. |
| **visualization-kit/** | Set of reusable React components: RadarChart, HeatMap, SWOTQuadrant, TrendLine, ComparisonBar. Encapsulates Chart.js / D3 configuration defaults aligned with India-optimized colour palette. |

---

### 4. `database/` – Data Layer Assets

| Folder | Contents |
|--------|----------|
| **migrations/** | Version-controlled JavaScript/TypeScript migration files run via [Migrate Mongo](https://github.com/seppevs/migrate-mongo) or Atlas CLI. Handles schema changes, index creation, seed roles. |
| **seed/** | Scripts to populate dev/QA environments with realistic synthetic data (1–2 k students, multilingual names, Indian board marks). |
| **schema-diagrams/** | ER & UML class diagrams exported as PNG/SVG plus editable draw.io source. Useful for onboarding and presentations. |

---

### 5. `infra/` – Infrastructure-as-Code & Deployment

| Path | Role |
|------|------|
| **terraform/** | Modules for provisioning MongoDB Atlas clusters (Mumbai region), Vercel projects, AWS S3 (backup), CloudFront CDN (optional). Secure secrets via Vault or AWS Secrets Manager. |
| **vercel/** | `vercel.json`, build-output-config, serverless function settings, environment variable mapping, analytics config. |

---

### 6. `docs/` – Documentation

- **architecture/**: high-level diagrams, ADRs (Architecture Decision Records)
- **api/**: OpenAPI/Swagger specs auto-generated from `api-server`
- **ui-ux/**: Figma exports, UI copy docs, accessibility guidelines
- **legal/**: India data-protection compliance docs, privacy policy templates
- **training/**: Teacher & admin training manuals; ML model training guide

---

### 7. `tests/` – Quality Assurance

| Sub-folder | Scope |
|------------|-------|
| **unit/** | Jest (TS) & Pytest suites for individual functions/modules. |
| **integration/** | Supertest scripts hitting the running API plus Mongo test containers. |
| **e2e/** | Playwright/Cypress user-flow tests across web frontend + backend. |
| **load/** | K6/Locust scripts to benchmark API & AI service throughput. |

---

### 8. Continuous Integration / Delivery

`/.github/workflows/` contains:

- **ci.yml** – Lint, test, build on PR
- **deploy-vercel.yml** – Auto-deploy frontend & serverless functions to Vercel on main branch
- **docker-ai.yml** – Build & push AI service image to registry, trigger Supabase deploy
- **security-scan.yml** – Snyk/OWASP dependency vulnerability checks

---

### 9. Environment & Tooling

| File | Purpose |
|------|---------|
| **.env.example** | Reference for required environment variables (Mongo connection strings, JWT secrets, OpenAI keys, Supabase URL). |
| **package.json** | Declares workspace packages, scripts (`dev`, `build`, `lint`, `format`, `test`, `start`). |
| **README.md** | Quick-start, architecture snapshot, contribution guidelines. |

---

### 10. Development Workflow Summary

1. **Clone** monorepo → `pnpm install` or `yarn`
2. **Run** all services with `pnpm dev`:  
   - Vite dev server on `localhost:5173`  
   - API server on `localhost:4000`  
   - AI service via Docker compose on `localhost:8000`
3. **Database**: Start local MongoDB or connect to Atlas dev cluster. Apply `migrate` then `seed`.
4. **Testing**: `pnpm test` (unit), `pnpm test:e2e`
5. **Commit & Push** → GitHub Actions run CI → Vercel preview deploy generated.

---

### 11. How Components Interact

```mermaid
sequenceDiagram
    participant UI as Web Frontend
    participant API as Node API Server
    participant AI as Python AI Service
    participant DB as MongoDB Atlas

    UI->>API: Auth (JWT)
    UI->>API: CRUD student data
    API->>DB: Read/Write documents
    API->>AI: POST /generate-swot (studentId, term)
    AI-->>API: SWOT JSON + recommendations
    API-->>DB: Store analysis
    API-->>UI: Return enriched response
```

*Mermaid diagram shown for conceptual flow; actual runtime uses REST/HTTP + WebSocket for live updates.*

---

## Conclusion

This project structure ensures:

- **Clear separation of concerns**: UI, API, AI, and shared packages isolated yet collaborative.
- **Scalability & Maintainability**: Modular packages and services enable independent deployment, testing, and scaling.
- **India-ready**: Data models, localization, and infrastructure tuned for Indian schools.
- **Developer Experience**: Monorepo with unified tooling, consistent schemas, and automated deployments.

Use this document as the single source of truth when navigating or extending the Student SWOT Analysis Platform repository.  
