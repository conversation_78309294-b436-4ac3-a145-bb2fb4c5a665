{
  "version": 2,
  "name": "student-swot-frontend",

  // --- Build Configuration ---
  // This section tells <PERSON><PERSON><PERSON> how to build your Vite project.
  "builds": [
    {
      // "src" points to the file that defines your project's dependencies and scripts.
      // Assuming this vercel.json is in the root of your web-frontend app.
      "src": "package.json",
      // "@vercel/static-build" is a versatile builder for static site generators and front-end frameworks.
      "use": "@vercel/static-build",
      "config": {
        // Optional: Vercel usually detects these from your package.json.
        // Uncomment and set if you have custom commands or non-standard setup.
        // "installCommand": "npm install", // or "yarn install", "pnpm install"
        // "buildCommand": "npm run build",   // or "yarn build", "pnpm build"

        // "distDir" is the directory where Vite outputs the built static files.
        // Vite's default is "dist". Change if your vite.config.js specifies a different output directory.
        "distDir": "dist"
      }
    }
  ],

  // --- Routing Rules ---
  // These rules are crucial for Single Page Applications (SPAs) like React apps
  // to handle client-side routing correctly.
  "routes": [
    // This rule tells Vercel to first try to serve any static files that match the request path
    // from the output directory (e.g., "dist"). This handles your CSS, JS, images, etc.
    {
      "handle": "filesystem"
    },
    // If no file is found by the "filesystem" handler, this rule acts as a fallback.
    // It serves your main "index.html" file for any other path.
    // This allows your client-side router (e.g., React Router) to take over.
    {
      "src": "/.*", // Matches any path
      "dest": "/index.html" // Serves the entry point of your SPA
    }
  ],

  // --- Rewrites (Example for API Proxying) ---
  // Rewrites are processed before routes. Use this for proxying API requests
  // to a backend server to avoid CORS issues or to hide the backend URL.
  // "rewrites": [
  //   {
  //     "source": "/api/(.*)", // Matches requests to /api/...
  //     "destination": "https://your-actual-api-server.com/api/$1" // Proxies to your backend
  //   }
  // ],

  // --- Custom HTTP Headers ---
  "headers": [
    // Security Headers: Applied to all responses to enhance security.
    {
      "source": "/(.*)", // Apply to all paths
      "headers": [
        { "key": "X-Content-Type-Options", "value": "nosniff" },
        { "key": "X-Frame-Options", "value": "DENY" },
        { "key": "X-XSS-Protection", "value": "1; mode=block" }
        // Strict-Transport-Security (HSTS): Enforces HTTPS.
        // Enable this only after you are sure your entire site works correctly over HTTPS.
        // { "key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload" },

        // Content-Security-Policy (CSP): Helps prevent XSS and other injection attacks.
        // This requires careful configuration specific to your application's resources.
        // { "key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' https://trusted-cdn.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:; object-src 'none';" }
      ]
    },

    // Cache-Control for Hashed Static Assets (e.g., /assets/index.abcdef12.js)
    // Vite generates filenames with hashes for long-term caching.
    // "immutable" tells the browser it can cache these files indefinitely without re-checking.
    {
      "source": "/assets/(.*)",
      "headers": [
        { "key": "Cache-Control", "value": "public, max-age=31536000, immutable" }
      ]
    },

    // Cache-Control for other common static assets (images, fonts).
    {
      "source": "/(.*)\\.(jpg|jpeg|png|gif|svg|ico|webp|woff|woff2|ttf|eot)$",
      "headers": [
        { "key": "Cache-Control", "value": "public, max-age=2592000" } // Cache for 30 days
      ]
    },

    // Cache-Control for the main index.html file.
    // This ensures that users always get the latest version of the app shell,
    // which then loads the appropriate (possibly cached) JS/CSS assets.
    // "must-revalidate" forces revalidation with the server.
    {
      "source": "/index.html",
      "headers": [
        { "key": "Cache-Control", "value": "public, max-age=0, must-revalidate" }
        // Alternatively, for no caching at all:
        // { "key": "Cache-Control", "value": "no-cache, no-store, must-revalidate" }
      ]
    }
  ]

  // --- Environment Variables ---
  // Environment variables are NOT set directly in vercel.json for security reasons.
  // They should be configured in your Vercel Project Settings (Dashboard -> Settings -> Environment Variables).
  //
  // During the build process on Vercel, these variables are available via `process.env.YOUR_VARIABLE_NAME`.
  //
  // For Vite applications to expose environment variables to the CLIENT-SIDE code,
  // they must be prefixed with `VITE_` (e.g., `VITE_API_URL`).
  // In your React code, you would access them as `import.meta.env.VITE_API_URL`.
  //
  // Example of how you might *think* about them, but *do not put actual secrets here*:
  // "env": {
  //   "VITE_API_ENDPOINT": "https://your-app.com/api" // This would be set in Vercel UI
  // }
}
